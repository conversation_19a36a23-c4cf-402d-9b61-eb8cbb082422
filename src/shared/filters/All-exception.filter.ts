import { Logger, Catch, ArgumentsHost, HttpException } from "@nestjs/common";
import { ZodSerializationException } from "nestjs-zod";
import {
  isDrizzlePostgresException as isPostgresError,
  PostgresException,
} from "../../utils/exception.util.js";
import { BaseExceptionFilter } from "@nestjs/core";
import type { Response } from "express";
import { ApiResponse } from "../interceptors/transformResponseInterceptor.js";
@Catch()
export class AllExceptionFilter extends BaseExceptionFilter {
  private readonly logger = new Logger(AllExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    if (exception instanceof ZodSerializationException) {
      const zodError = exception.getZodError();
      this.logger.error(`ZodSerializationException: ${zodError.message}`);
    }

    const ctx = host.switchToHttp();
    const res = ctx.getResponse<Response>();

    if (exception instanceof HttpException) {
      const statusCode = exception.getStatus();
      res.status(exception.getStatus()).json({
        statusCode,
        message: exception.message,
        data: null,
      } satisfies ApiResponse<null>);
      return;
    }

    if (isPostgresError(exception)) {
      throw new PostgresException(exception.message, {
        code: exception.cause.code,
        cause: exception.cause,
        severity: exception.cause.severity,
        detail: exception.cause.detail,
        constraint: exception.cause.constraint,
      });
    }

    res.status(500).json({
      statusCode: 500,
      message: "Internal server error",
      data: null,
    } satisfies ApiResponse<null>);
  }
}
