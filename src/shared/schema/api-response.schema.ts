import { z, ZodObject, ZodRawShape } from "zod";

export function createSingleResourceResponseSchema<T extends ZodRawShape>(
  schema: ZodObject<T>,
  isArray = false,
) {
  return z.object({
    statusCode: z.number(),
    message: z.string(),
    data: isArray ? z.array(schema) : schema,
  });
}

export function createPaginatedResourceResponseSchema<T extends ZodRawShape>(
  schema: ZodObject<T>,
) {
  return z.object({
    statusCode: z.number(),
    message: z.string(),
    data: z.object({
      items: z.array(schema),
      total: z.number(),
    }),
  });
}
