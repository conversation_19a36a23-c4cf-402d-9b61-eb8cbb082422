import { Role } from "../../roles/types/roles.types.js";
import { User } from "../../users/types/users.types.js";

export interface SmsLoginResponse {
  accessToken: string;
  role: Role["code"];
  refreshToken?: string;
}

export interface PlatformLoginResponse extends SmsLoginResponse {
  refreshToken: string;
}
export type TokenPayload = Pick<User, "email"> & {
  sub: User["id"];
  role: Role["code"];
};

export type VerifiedTokenPayload = TokenPayload & {
  iat: number;
  exp: number;
};

export interface AuthenticatedUser {
  id: string;
  email: string;
  role: number;
  activeAcademicSessionId?: string;
}

declare module "express-serve-static-core" {
  interface Request {
    user: AuthenticatedUser;
  }
}
