import {
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from "@nestjs/common";
import { TokenPayload, VerifiedTokenPayload } from "./type/auth.type.js";
import { JwtService } from "@nestjs/jwt";
import { jwtConstants } from "./constants/jwt-constants.js";
import { loginCredentials } from "./dto/login-user.dto.js";
import { PasswordService } from "../../shared/services/password.service.js";
import { RolesService } from "../roles/roles.service.js";
import { UsersService } from "../users/users.service.js";
import {
  PLATFORM_ROLES,
  SMS_ROLES,
} from "../roles/constants/roles.constants.js";
import { User } from "../users/types/users.types.js";
import { Role } from "../roles/types/roles.types.js";
import { omitKeysFromObject } from "../../utils/object-utils.js";
import { PlatformAdminsService } from "../../packages/platform/platform-admins/platform-admins.service.js";
import { PlatformLoginResponse, SmsLoginResponse } from "./type/auth.type.js";
import { SubscriptionsService } from "../../packages/platform/subscriptions/subscriptions.service.js";

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  public constructor(
    private readonly userService: UsersService,
    private readonly jwtService: JwtService,
    private readonly passwordService: PasswordService,
    private readonly rolesService: RolesService,
    private readonly subscriptionsService: SubscriptionsService,
    private readonly platformAdminService: PlatformAdminsService,
  ) {}

  /*
  |--------------------------------------------------------------------------
  | SMS Users Login
  |-------------------------------------------------------------------------- 
  |
  | This method is used to login all users of School Management System. 
  |
  */
  public async smsUsersLogin({
    email,
    password,
  }: loginCredentials): Promise<SmsLoginResponse> {
    const user = await this.findSmsUserOrThrow(email);

    // check if user password is correct
    await this.matchPasswordOrThrow(password, user.password);

    const userRole = await this.findRoleOrThrow(user.roleId);

    // check if user has any of the allowed roles
    this.validateUserHasSmsRole(userRole);

    // create jwt access token with user payload
    const payload: TokenPayload = {
      sub: user.id,
      email: user.email,
      role: userRole.code,
    };

    const accessToken = await this.createAccessToken(payload);

    // don't create refresh token if user is trying to sign in with temporary password
    if (user.isPasswordTemporary) {
      return {
        accessToken,
        role: userRole.code,
      };
    }

    const refreshToken = await this.createRefreshToken(payload);

    return {
      accessToken,
      role: userRole.code,
      refreshToken,
    };
  }

  /*
  |--------------------------------------------------------------------------
  | Platform Admin Login
  |-------------------------------------------------------------------------- 
  |
  | This method is used to login platform admins. 
  |
  */
  public async platformAdminLogin(
    signInPayload: loginCredentials,
  ): Promise<PlatformLoginResponse> {
    try {
      const existingPlatformAdmin = await this.findPlatformAdminOrThrow(
        signInPayload.email,
      );

      // check if user password is correct
      await this.matchPasswordOrThrow(
        signInPayload.password,
        existingPlatformAdmin.password,
      );

      const userRole = await this.findRoleOrThrow(existingPlatformAdmin.roleId);

      this.validateUserHasPLatformRole(userRole);

      // create jwt access token with user payload
      const payload: TokenPayload = {
        sub: existingPlatformAdmin.userId,
        email: existingPlatformAdmin.email,
        role: userRole.code,
      };

      const accessToken = await this.jwtService.signAsync(payload);

      // create refresh token
      const refreshToken = this.jwtService.sign(payload, {
        secret: jwtConstants.refreshToken.secret,
        expiresIn: jwtConstants.refreshToken.expireTimeInSec,
      });

      return {
        accessToken,
        role: userRole.code,
        refreshToken,
      };
    } catch (error: unknown) {
      this.logger.error("Failed to login platform admin", error);
      throw error;
    }
  }

  public async setPassword({
    userId,
    password,
  }: {
    userId: User["id"];
    password: User["password"];
  }): Promise<void> {
    const user = await this.userService.findById(userId);

    if (!user) {
      throw new NotFoundException("User not found");
    }

    if (!user.isPasswordTemporary) {
      throw new UnauthorizedException("Password already set.");
    }

    await this.userService.updateUserPassword(userId, {
      password,
      isPasswordTemporary: false,
    });
  }

  public async refreshToken(token: string | undefined) {
    if (!token) {
      throw new UnauthorizedException("Missing refresh token");
    }
    try {
      const payload = await this.verifyRefreshToken(token);

      const accessToken = await this.createAccessToken(payload);

      return {
        accessToken,
        role: payload.role,
      };
    } catch (error) {
      this.logger.error("Failed to refresh token", error);
      throw new UnauthorizedException("Invalid or expired refresh token");
    }
  }

  private validateUserHasPLatformRole(userRole: Role): void {
    const isUserAllowed = Object.values(PLATFORM_ROLES).find(
      role => role.code === userRole.code,
    );
    if (!isUserAllowed) {
      throw new ForbiddenException(
        "You do not have the required permissions to access this resource",
      );
    }
  }

  private validateUserHasSmsRole(userRole: Role): void {
    const isUserAllowed = Object.values(SMS_ROLES).find(
      role => role.code === userRole.code,
    );
    if (!isUserAllowed) {
      throw new ForbiddenException(
        "You do not have the required permissions to access this resource",
      );
    }
  }

  private async verifyRefreshToken(token: string): Promise<TokenPayload> {
    try {
      const response = await this.jwtService.verifyAsync<VerifiedTokenPayload>(
        token,
        {
          secret: jwtConstants.refreshToken.secret,
        },
      );

      return omitKeysFromObject(response, ["iat", "exp"]);
    } catch (error) {
      this.logger.error("Failed to verify refresh token", error);
      throw new UnauthorizedException("Invalid or expired refresh token");
    }
  }

  private async createAccessToken(payload: TokenPayload): Promise<string> {
    try {
      return await this.jwtService.signAsync(payload);
    } catch (error: unknown) {
      this.logger.error("Failed to create access token", error);
      throw error;
    }
  }

  private async createRefreshToken(payload: TokenPayload): Promise<string> {
    try {
      return await this.jwtService.signAsync(payload, {
        secret: jwtConstants.refreshToken.secret,
        expiresIn: jwtConstants.refreshToken.expireTimeInSec,
      });
    } catch (error: unknown) {
      this.logger.error("Failed to create refresh token", error);
      throw error;
    }
  }

  private async matchPasswordOrThrow(
    password: string,
    hashedPassword: string,
  ): Promise<void> {
    const doPasswordsMatch = await this.passwordService.compare(
      password,
      hashedPassword,
    );

    if (!doPasswordsMatch) {
      throw new UnauthorizedException("Incorrect password");
    }
  }

  private async findRoleOrThrow(roleId: Role["id"]): Promise<Role> {
    let role;
    try {
      role = await this.rolesService.findById(roleId);
    } catch (error: unknown) {
      this.logger.error("Failed to find role", error);
      throw error;
    }

    if (!role) {
      this.logger.warn(`Role with id: ${roleId} not found`);
      throw new ForbiddenException(
        "You do not have permission to access this resource.",
      );
    }
    return role;
  }

  private async findPlatformAdminOrThrow(email: string) {
    let platformAdmin;
    try {
      platformAdmin =
        await this.platformAdminService.findByEmailIncludePassword(email);
    } catch (error: unknown) {
      this.logger.error("Failed to find platform admin", error);
      throw error;
    }

    if (!platformAdmin) {
      throw new NotFoundException("No platform admin found with this email");
    }
    return platformAdmin;
  }

  private async findSmsUserOrThrow(email: string): Promise<User> {
    let user;
    try {
      user = await this.userService.findByEmailAndIncludePassword(email);
    } catch (error: unknown) {
      this.logger.error("Failed to find user", error);
      throw error;
    }

    if (!user) {
      throw new NotFoundException("No user found with this email");
    }
    return user;
  }
}
