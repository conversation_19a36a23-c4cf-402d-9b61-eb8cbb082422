import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import { createSingleResourceResponseSchema } from "../../../shared/schema/api-response.schema.js";

/**
 * Base login response schema containing common fields
 * for both SMS and Platform login responses
 */
const baseLoginResponseSchema = z.object({
  accessToken: z
    .string()
    .describe("JWT access token for authenticating API requests"),
  role: z.number().describe("User role code (e.g., 3000)"),
});

/**
 * SMS login response schema
 * Used for SMS system users (teachers, students, staff, guardians, etc.)
 */
const smsLoginResponseSchema = baseLoginResponseSchema.extend({
  refreshToken: z
    .string()
    .optional()
    .describe(
      "JWT refresh token (only provided if user doesn't have temporary password)",
    ),
});

/**
 * Platform login response schema
 * Used for platform administrators
 */
const platformLoginResponseSchema = baseLoginResponseSchema;

/**
 * Refresh token response schema
 * Used when refreshing access tokens
 */
const refreshTokenResponseSchema = z.object({
  accessToken: z
    .string()
    .describe("New JWT access token for authenticating API requests"),
  role: z.number().describe("User role code (e.g., 3000)"),
});

// Export DTO classes for Swagger documentation
export class SmsLoginResponseDto extends createZodDto(
  createSingleResourceResponseSchema(smsLoginResponseSchema),
) {}

export class PlatformLoginResponseDto extends createZodDto(
  createSingleResourceResponseSchema(platformLoginResponseSchema),
) {}

export class RefreshTokenResponseDto extends createZodDto(
  createSingleResourceResponseSchema(refreshTokenResponseSchema),
) {}

// Export types for use in services
export type SmsLoginResponse = z.infer<typeof smsLoginResponseSchema>;
export type PlatformLoginResponse = z.infer<typeof platformLoginResponseSchema>;
export type RefreshTokenResponse = z.infer<typeof refreshTokenResponseSchema>;
