import { <PERSON>du<PERSON> } from "@nestjs/common";
import { AuthService } from "./auth.service.js";
import { UsersModule } from "../users/users.module.js";
import { AuthController } from "./auth.controller.js";
import { RolesModule } from "../roles/roles.module.js";
import { PlatformAdminsModule } from "../../packages/platform/platform-admins/platform-admins.module.js";
import { SubscriptionsModule } from "../../packages/platform/subscriptions/subscriptions.module.js";

@Module({
  imports: [
    UsersModule,
    RolesModule,
    PlatformAdminsModule,
    SubscriptionsModule,
  ],
  controllers: [AuthController],
  providers: [AuthService],
})
export class AuthModule {}
