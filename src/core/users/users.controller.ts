import { Controller, Get } from "@nestjs/common";
import { UsersService } from "./users.service.js";
import { ApiTags, ApiExtraModels } from "@nestjs/swagger";
import {
  BaseUserProfileResponseDto,
  InstituteOwnerProfileResponseDto,
  TeacherProfileResponseDto,
} from "./dto/user-profile-response.dto.js";
import { ApiDocGetCurrentUserProfile } from "./docs/users.docs.js";
import type { AuthenticatedUser } from "../auth/type/auth.type.js";
import { User } from "../../shared/decorators/user.decorator.js";

@Controller()
@ApiTags("Users")
@ApiExtraModels(
  BaseUserProfileResponseDto,
  InstituteOwnerProfileResponseDto,
  TeacherProfileResponseDto,
)
export class UsersController {
  public constructor(private readonly usersService: UsersService) {}

  /**
   * Get current authenticated user's profile
   * Returns different profile structures based on user role
   */
  @Get(["sms/users/me", "platform/users/me"])
  @ApiDocGetCurrentUserProfile()
  public async getCurrentUserProfile(@User() user: AuthenticatedUser) {
    return await this.usersService.getProfile(user.id, user.role);
  }
}
