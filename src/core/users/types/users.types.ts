import { createUserSchema, userProfileSchema } from "../dto/user.dto.js";
import { z } from "zod";
import { usersTable } from "../../../db/schemas/auth.schema.js";

export type User = typeof usersTable.$inferSelect;
export type NewUser = typeof usersTable.$inferInsert;
export type UserUpdate = Partial<NewUser>;

// Base user profile
export type UserProfile = z.infer<typeof userProfileSchema>;

// UserProfile with temporary password field
export type UserWithTempPassword = UserProfile & {
  tempPassword: string;
};

// Input payload for creating user
export type CreateUserPayload = z.infer<typeof createUserSchema>;
