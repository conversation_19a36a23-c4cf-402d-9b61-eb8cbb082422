import {
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import { PasswordService } from "../../shared/services/password.service.js";
import { NewUser, User, UserProfile } from "./types/users.types.js";
import { OwnersService } from "../../packages/sms/institute-owners/owners.service.js";
import { ROLES } from "../roles/constants/roles.constants.js";
import { Role } from "../roles/types/roles.types.js";
import { StaffService } from "../../packages/sms/staff/staff.service.js";
import {
  BranchAdminProfile,
  TeacherProfile,
} from "../../packages/sms/staff/types/staff.types.js";
import { InjectDrizzle } from "../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../db/type.js";
import { ServiceOptions } from "../../shared/types/shared.types.js";
import { and, eq, getTableColumns, SQL } from "drizzle-orm";
import { roleTable, usersTable } from "../../db/schemas/index.js";
import { omitKeysFromObject } from "../../utils/object-utils.js";
import { executeQueryTakeFirstOrThrow } from "../../utils/pg-utils.js";

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  public constructor(
    @InjectDrizzle() private readonly db: Database,
    private readonly passwordService: PasswordService,
    private readonly instituteOwnersService: OwnersService,
    private readonly staffService: StaffService,
  ) {}

  public async findByEmailAndIncludePassword(
    email: User["email"],
    options?: ServiceOptions,
  ): Promise<User | undefined> {
    const client = options?.pgTrx ?? this.db;
    try {
      return await client.query.usersTable.findFirst({
        where: eq(usersTable.email, email),
      });
    } catch (error: unknown) {
      this.logger.error("Failed to get user by email", error);
      throw error;
    }
  }

  public async getProfile(
    userId: User["id"],
    roleCode: Role["code"],
  ): Promise<UserProfile | BranchAdminProfile | TeacherProfile> {
    try {
      switch (roleCode) {
        case ROLES.INSTITUTE_OWNER.code: {
          return await this.instituteOwnersService.findFirstOrThrow({
            userId,
          });
        }
        case ROLES.BRANCH_ADMIN.code: {
          return await this.staffService.getBranchAdminProfile(userId);
        }
        case ROLES.TEACHER.code: {
          return await this.staffService.getTeacherProfile(userId);
        }
        default: {
          const user = await this.findByIdOrThrow(userId);
          return user;
        }
      }
    } catch (error: unknown) {
      if (!(error instanceof NotFoundException)) {
        this.logger.error(
          `Failed to get user profile for user: ${userId}`,
          error,
        );
      }
      throw error;
    }
  }

  public async findById(
    id: User["id"],
    options?: ServiceOptions,
  ): Promise<UserProfile | undefined> {
    const dbClient = options?.pgTrx ?? this.db;
    try {
      const rawUser = await dbClient.query.usersTable.findFirst({
        where: eq(usersTable.id, id),
        columns: {
          password: false,
        },
        with: {
          role: true,
        },
      });
      if (!rawUser) return;

      const { role, ...user } = rawUser;
      return {
        ...user,
        role: role.code,
      };
    } catch (error: unknown) {
      this.logger.error(`Failed to get user by id: ${id}`, error);
      throw error;
    }
  }

  public async create(
    newUserData: NewUser,
    options: ServiceOptions,
  ): Promise<UserProfile> {
    const duplicateUser = await this.findByEmailAndIncludePassword(
      newUserData.email,
      options,
    );

    if (duplicateUser) {
      throw new ConflictException("This email is already registered");
    }
    try {
      const hashPassword = await this.passwordService.hash(
        newUserData.password,
      );

      return await this.db.transaction(async pgTrx => {
        const dbClient = options.pgTrx ?? pgTrx;
        const { id: newUserId } = await executeQueryTakeFirstOrThrow(
          dbClient
            .insert(usersTable)
            .values({
              ...newUserData,
              password: hashPassword,
            })
            .returning(),
        );
        return await this.findByIdOrThrow(newUserId, { pgTrx: dbClient });
      });
    } catch (error) {
      this.logger.error("Failed to create user");
      throw error;
    }
  }

  public async findByIdOrThrow(
    id: User["id"],
    options?: ServiceOptions,
  ): Promise<UserProfile> {
    let existingUser: UserProfile | undefined;
    try {
      existingUser = await this.findById(id, options);
    } catch (error: unknown) {
      this.logger.error("Failed to find user by id", error);
      throw error;
    }
    if (!existingUser) {
      throw new NotFoundException(`User with id: '${id}' does not exist`);
    }
    return existingUser;
  }

  public async updateUserPassword(
    userId: User["id"],
    data: Pick<User, "password" | "isPasswordTemporary">,
  ): Promise<void> {
    await this.checkExistingUser(userId);

    try {
      const hashPassword = await this.passwordService.hash(data.password);
      await executeQueryTakeFirstOrThrow(
        this.db
          .update(usersTable)
          .set({
            isPasswordTemporary: false,
            password: hashPassword,
          })
          .returning(),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to update user");
      throw error;
    }
  }

  private async checkExistingUser(userId: User["id"]) {
    let existingUser: UserProfile | undefined;

    try {
      existingUser = await this.findById(userId);
    } catch (error: unknown) {
      this.logger.error("Failed to check if user exists", error);
      throw error;
    }

    if (!existingUser) {
      throw new NotFoundException(`User with id: '${userId}' does not exist`);
    }
  }

  public async findAll(
    criteria?: Partial<Pick<User, "id">>,
    includePassword = false,
  ): Promise<UserProfile[]> {
    const filters: SQL[] = [];

    if (criteria?.id) {
      filters.push(eq(usersTable.id, criteria.id));
    }
    const userFields = getTableColumns(usersTable);
    const safeUserFields = includePassword
      ? userFields
      : omitKeysFromObject(userFields, ["password"]);

    try {
      return await this.db
        .select({
          ...safeUserFields,
          role: roleTable.code,
        })
        .from(usersTable)
        .innerJoin(roleTable, eq(roleTable.id, usersTable.roleId))
        .where(and(...filters));
    } catch (error: unknown) {
      this.logger.error("Failed to find all users", error);
      throw error;
    }
  }
}
