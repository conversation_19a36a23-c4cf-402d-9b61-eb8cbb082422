import { Injectable, Logger } from "@nestjs/common";
import { NewRole, Role } from "./types/roles.types.js";
import { ROLES } from "./constants/roles.constants.js";
import { ServiceOptions } from "../../shared/types/shared.types.js";
import { executeQueryTakeFirstOrThrow } from "../../utils/pg-utils.js";
import { roleTable } from "../../db/schemas/index.js";
import type { Database } from "../../db/type.js";
import { and, eq, SQL } from "drizzle-orm";
import { InjectDrizzle } from "../../shared/modules/drizzle/drizzle.decorators.js";

@Injectable()
export class RolesService {
  private readonly logger = new Logger(RolesService.name);

  public constructor(@InjectDrizzle() public db: Database) {}

  public async create(
    newRoleData: NewRole,
    options?: ServiceOptions,
  ): Promise<Role> {
    const dbClient = options?.pgTrx ?? this.db;
    try {
      return await executeQueryTakeFirstOrThrow(
        dbClient.insert(roleTable).values(newRoleData).returning(),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to create role", error);
      throw error;
    }
  }

  public async findById(id: Role["id"]): Promise<Role | undefined> {
    try {
      return await this.db.query.roleTable.findFirst({
        where: eq(roleTable.id, id),
      });
    } catch (error: unknown) {
      this.logger.error(`Failed to get role by id: ${id}`, error);
      throw error;
    }
  }

  public async findOne(
    criteria: Partial<Role>,
    options?: ServiceOptions,
  ): Promise<Role | undefined> {
    const filters: SQL[] = [];

    if (criteria.id) {
      filters.push(eq(roleTable.id, criteria.id));
    }

    if (criteria.name) {
      filters.push(eq(roleTable.name, criteria.name));
    }

    if (criteria.code) {
      filters.push(eq(roleTable.code, criteria.code));
    }

    const dbClient = options?.pgTrx ?? this.db;
    try {
      return await dbClient.query.roleTable.findFirst({
        where: and(...filters),
      });
    } catch (error: unknown) {
      this.logger.error(`Failed to get role by criteria: `, criteria, error);
      throw error;
    }
  }

  public async findByNameOrCreate(
    name: Role["name"],
    options?: ServiceOptions,
  ): Promise<Role> {
    try {
      const role = await this.findOne({ name }, options);
      if (!role) {
        return await this.create(ROLES[name], options);
      }
      return role;
    } catch (error: unknown) {
      this.logger.error(`Failed to get role by name: ${name}`, error);
      throw error;
    }
  }
}
