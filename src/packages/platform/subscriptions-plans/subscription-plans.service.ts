import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { ListAllEntitiesQueryOptions } from "../../../shared/types/shared.types.js";
import {
  NewSubscriptionPlan,
  SubscriptionPlan,
  SubscriptionPlanUpdate,
} from "./types/subscription-plans.types.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertOrUpdateException,
} from "../../../utils/pg-utils.js";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../db/type.js";
import { subscriptionPlanTable } from "../../../db/schemas/index.js";
import { eq } from "drizzle-orm";

@Injectable()
export class SubscriptionPlansService {
  private readonly logger = new Logger(SubscriptionPlansService.name);

  public constructor(@InjectDrizzle() private readonly db: Database) {}

  public async create(data: NewSubscriptionPlan) {
    try {
      return await executeQueryTakeFirstOrThrow(
        this.db.insert(subscriptionPlanTable).values(data).returning(),
      );
    } catch (error: unknown) {
      handleDatabaseInsertOrUpdateException(error, {
        resource: "subscriptionPlan",
        logger: this.logger,
      });
    }
  }

  public async update(
    id: SubscriptionPlan["id"],
    data: SubscriptionPlanUpdate,
  ) {
    try {
      return await executeQueryTakeFirstOrThrow(
        this.db
          .update(subscriptionPlanTable)
          .set(data)
          .where(eq(subscriptionPlanTable.id, id))
          .returning(),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to update software package", error);
      throw error;
    }
  }

  public async delete(id: SubscriptionPlan["id"]) {
    const existingSubscriptionPlan = await this.findById(id);

    if (!existingSubscriptionPlan) {
      throw new NotFoundException(
        `Software Package with id: '${id}' does not exist`,
      );
    }

    try {
      await this.db
        .delete(subscriptionPlanTable)
        .where(eq(subscriptionPlanTable.id, id))
        .execute();
    } catch (error: unknown) {
      this.logger.error("Failed to delete software package", error);
      throw error;
    }
  }

  public async getAll({ limit, offset }: ListAllEntitiesQueryOptions) {
    return await this.db.query.subscriptionPlanTable.findMany({
      limit,
      offset,
    });
  }

  public async findById(id: SubscriptionPlan["id"]) {
    return await this.db.query.subscriptionPlanTable.findFirst({
      where: eq(subscriptionPlanTable.id, id),
    });
  }
}
