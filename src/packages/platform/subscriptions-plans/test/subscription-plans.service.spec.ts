import { Test } from "@nestjs/testing";
import { SubscriptionPlansService } from "../subscription-plans.service.js";
import { KyselyClientMockProvider } from "../../../../../tests/mocks/kysely-mock.js";

describe("SubscriptionPlanService", () => {
  let service: SubscriptionPlansService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [SubscriptionPlansService, KyselyClientMockProvider],
    }).compile();

    service = module.get<SubscriptionPlansService>(SubscriptionPlansService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("create", () => {
    describe("on valid input", () => {
      it("should create a new package", () => {
        console.log();
      });
    });
  });
});
