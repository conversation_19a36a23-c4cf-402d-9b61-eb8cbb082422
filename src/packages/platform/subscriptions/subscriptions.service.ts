import { Injectable, Logger } from "@nestjs/common";
import { Kysely } from "kysely";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { ListAllEntitiesQueryOptions } from "../../../shared/types/shared.types.js";
import { DB } from "../../../database/types.js";
import { NewSubscription } from "./types/subscription.types.js";
import type { Database } from "../../../db/type.js";
import { subscriptionTable } from "../../../db/schemas/index.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertException,
} from "../../../utils/pg-utils.js";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";

@Injectable()
export class SubscriptionsService {
  private readonly logger = new Logger(SubscriptionsService.name);

  public constructor(@InjectDrizzle() private readonly db: Database) {}

  public async findAll(queryOptions: ListAllEntitiesQueryOptions) {
    try {
      return await this.db.query.subscriptionTable.findMany({
        limit: queryOptions.limit,
        offset: queryOptions.offset,
      });
    } catch (error: unknown) {
      this.logger.error("Failed to get all subscriptions", error);
      throw error;
    }
  }

  public async create(data: NewSubscription) {
    try {
      return await executeQueryTakeFirstOrThrow(
        this.db.insert(subscriptionTable).values(data).returning(),
      );
    } catch (error: unknown) {
      handleDatabaseInsertException(error, {
        resource: "subscription",
        logger: this.logger,
      });
    }
  }
}
