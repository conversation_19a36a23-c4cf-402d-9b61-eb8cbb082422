import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
} from "@nestjs/common";
import type { Database } from "../../../db/type.js";
import {
  subscriptionTable,
  subscriptionPlanTable,
  usersTable,
} from "../../../db/schemas/index.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertOrUpdateException,
} from "../../../utils/pg-utils.js";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import {
  NewSubscription,
  Subscription,
  SubscriptionResponse,
} from "./types/subscription.types.js";

import { and, eq, sql, SQL } from "drizzle-orm";
import { ServiceOptions } from "../../../shared/types/shared.types.js";

@Injectable()
export class SubscriptionsService {
  private readonly logger = new Logger(SubscriptionsService.name);

  public constructor(@InjectDrizzle() private readonly db: Database) {}

  public async findAll(queryOptions: {
    limit?: number;
    offset?: number;
    status?: "ACTIVE" | "CANCELLED";
    ownerId?: string;
  }) {
    const filterOptions = {
      status: queryOptions.status,
      ownerId: queryOptions.ownerId,
    };
    try {
      const [items, total] = await Promise.all([
        this.buildSelectQuery(this.buildQueryFilters(filterOptions))
          .limit(queryOptions.limit ?? 10)
          .offset(queryOptions.offset ?? 0),

        this.db.$count(
          subscriptionTable,
          and(...this.buildQueryFilters(filterOptions)),
        ),
      ]);

      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to get all subscriptions", error);
      throw error;
    }
  }

  public async create(data: NewSubscription) {
    try {
      // Check if owner already has a subscription
      const existingSubscription = await this.db
        .select()
        .from(subscriptionTable)
        .where(eq(subscriptionTable.ownerId, data.ownerId))
        .limit(1);

      if (existingSubscription.length > 0) {
        throw new ConflictException(
          "Owner already has a subscription. Only one subscription per owner is allowed.",
        );
      }

      return await executeQueryTakeFirstOrThrow(
        this.db.insert(subscriptionTable).values(data).returning(),
      );
    } catch (error: unknown) {
      if (error instanceof ConflictException) {
        throw error;
      }
      handleDatabaseInsertOrUpdateException(error, {
        resource: "subscription",
        logger: this.logger,
      });
    }
  }

  public async findByIdOrThrow(subscriptionId: string) {
    try {
      const subscription = await this.findFirst({ id: subscriptionId });

      if (!subscription) {
        throw new NotFoundException("Subscription not found");
      }

      return subscription;
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error("Failed to find subscription by ID", error);
      throw error;
    }
  }

  public async findByOwnerIdOrThrow(ownerId: string) {
    try {
      const subscription = await this.findFirst({ ownerId });

      if (!subscription) {
        throw new NotFoundException("No subscription found for this owner");
      }

      return subscription;
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error("Failed to find subscription by owner ID", error);
      throw error;
    }
  }

  public async findFirst(
    criteria: Partial<Pick<Subscription, "status" | "ownerId" | "id">>,
  ) {
    try {
      return (await this.buildSelectQuery(this.buildQueryFilters(criteria)))[0];
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error("Failed to find subscription", error);
      throw error;
    }
  }

  public async update(
    subscriptionId: string,
    updateData: Partial<
      Pick<
        Subscription,
        "planId" | "status" | "paymentCycle" | "endDate" | "lastPaymentDate"
      >
    >,
  ) {
    try {
      return await executeQueryTakeFirstOrThrow(
        this.db
          .update(subscriptionTable)
          .set(updateData)
          .where(eq(subscriptionTable.id, subscriptionId))
          .returning(),
      );
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error("Failed to update subscription", error);
      handleDatabaseInsertOrUpdateException(error, {
        resource: "subscription",
      });
    }
  }

  public async cancel(subscriptionId: string, _reason?: string) {
    try {
      // Check if subscription exists and is active
      const subscription = await this.findByIdOrThrow(subscriptionId);

      if (subscription.status === "CANCELLED") {
        throw new ConflictException("Subscription is already cancelled");
      }

      return await executeQueryTakeFirstOrThrow(
        this.db
          .update(subscriptionTable)
          .set({
            status: "CANCELLED",
            cancellationDate: new Date(),
          })
          .where(eq(subscriptionTable.id, subscriptionId))
          .returning(),
      );
    } catch (error: unknown) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      this.logger.error("Failed to cancel subscription", error);
      handleDatabaseInsertOrUpdateException(error, {
        resource: "subscription",
      });
    }
  }

  private buildSelectQuery(filters: SQL[], options?: ServiceOptions) {
    const dbClient = options?.pgTrx ?? this.db;
    return dbClient
      .select({
        id: subscriptionTable.id,
        status: subscriptionTable.status,
        paymentCycle: subscriptionTable.paymentCycle,
        startDate: subscriptionTable.startDate,
        endDate: subscriptionTable.endDate,
        lastPaymentDate: subscriptionTable.lastPaymentDate,
        cancellationDate: subscriptionTable.cancellationDate,
        createdAt: subscriptionTable.createdAt,
        plan: sql<SubscriptionResponse["plan"]>`json_build_object(
              'id', ${subscriptionPlanTable.id},
              'title', ${subscriptionPlanTable.title},
              'description', ${subscriptionPlanTable.description},
              'price', ${subscriptionPlanTable.price},
              'setupCharges', ${subscriptionPlanTable.setupCharges},
              'branches', ${subscriptionPlanTable.branches},
              'students', ${subscriptionPlanTable.students},
              'features', ${subscriptionPlanTable.features}
            )`,
        owner: sql<SubscriptionResponse["owner"]>`json_build_object(
              'id', ${usersTable.id},
              'name', ${usersTable.name},
              'email', ${usersTable.email}
            )`,
      })
      .from(subscriptionTable)
      .innerJoin(
        subscriptionPlanTable,
        eq(subscriptionPlanTable.id, subscriptionTable.planId),
      )
      .innerJoin(usersTable, eq(usersTable.id, subscriptionTable.ownerId))
      .where(and(...filters));
  }

  private buildQueryFilters(queryOptions: {
    status?: Subscription["status"];
    ownerId?: string;
    id?: string;
  }) {
    const filters: SQL[] = [];

    if (queryOptions.status) {
      filters.push(eq(subscriptionTable.status, queryOptions.status));
    }

    if (queryOptions.id) {
      filters.push(eq(subscriptionTable.id, queryOptions.id));
    }

    if (queryOptions.ownerId) {
      filters.push(eq(subscriptionTable.ownerId, queryOptions.ownerId));
    }

    return filters;
  }
}
