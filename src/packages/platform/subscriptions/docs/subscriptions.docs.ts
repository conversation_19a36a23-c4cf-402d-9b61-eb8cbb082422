import { Api<PERSON>peration, ApiResponse, ApiParam } from "@nestjs/swagger";
import {
  useCommonCreateResourceApiResponses,
  useCommonListResourceDocs,
} from "../../../../docs/shared/api-responses.js";
import { SubscriptionResponseDto, ListSubscriptionsResponseDto } from "../dto/subscription-response.dto.js";

export function ApiDocCreateSubscription() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Create Subscription",
      description: "Creates a new subscription for an institute owner with the specified plan and payment cycle",
    }),
    ApiResponse({
      status: 201,
      description: "Returns the newly created subscription with complete details",
      type: SubscriptionResponseDto,
    }),
  );
}

export function ApiDocUpdateSubscription() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Update Subscription",
      description: "Updates an existing subscription. Only platform admins can update subscriptions.",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the updated subscription with complete details",
      type: SubscriptionResponseDto,
    }),
  );
}

export function ApiDocListSubscriptions() {
  return useCommonListResourceDocs(
    ApiOperation({
      summary: "List All Subscriptions",
      description: "Lists all subscriptions in the system. Only accessible by platform admins.",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the list of subscriptions with pagination",
      type: ListSubscriptionsResponseDto,
    }),
  );
}

export function ApiDocGetSubscriptionById() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Get Subscription by ID",
      description: "Retrieves a specific subscription by its ID. Platform admins can access any subscription.",
    }),
    ApiParam({
      name: "subscriptionId",
      required: true,
      description: "Unique identifier of the subscription",
      type: String,
      example: "123e4567-e89b-12d3-a456-426614174000",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the subscription details",
      type: SubscriptionResponseDto,
    }),
  );
}

export function ApiDocGetOwnerSubscription() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Get Owner's Subscription",
      description: "Retrieves the subscription for the authenticated institute owner. Owners can only view their own subscription.",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the owner's subscription details",
      type: SubscriptionResponseDto,
    }),
  );
}

export function ApiDocCancelSubscription() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Cancel Subscription",
      description: "Cancels an active subscription. Only platform admins can cancel subscriptions.",
    }),
    ApiParam({
      name: "subscriptionId",
      required: true,
      description: "Unique identifier of the subscription to cancel",
      type: String,
      example: "123e4567-e89b-12d3-a456-426614174000",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the cancelled subscription details",
      type: SubscriptionResponseDto,
    }),
  );
}
