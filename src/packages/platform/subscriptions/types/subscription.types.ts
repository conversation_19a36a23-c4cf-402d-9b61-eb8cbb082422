import { subscriptionTable } from "../../../../db/schemas/index.js";
import { subscriptionResponseSchema } from "../dto/subscription-response.dto.js";
import { z } from "zod";

export type NewSubscription = typeof subscriptionTable.$inferInsert;
export type Subscription = typeof subscriptionTable.$inferSelect;
export type SubscriptionUpdate = Partial<NewSubscription>;

export type SubscriptionResponse = z.infer<
  typeof subscriptionResponseSchema
>;
