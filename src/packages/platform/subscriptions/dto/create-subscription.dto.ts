import { z } from "zod";
import { createZodDto } from "nestjs-zod";
import { getUuidSchema } from "../../../../shared/schema/zod-common.schema.js";

export const subscriptionStatusSchema = z.enum(["ACTIVE", "CANCELLED"], {
  message: "Status must be either 'ACTIVE' or 'CANCELLED'",
});

export const subscriptionPaymentCycleSchema = z.enum(["MONTHLY", "YEARLY"], {
  message: "Payment cycle must be either 'MONTHLY' or 'YEARLY'",
});

export const createSubscriptionBaseSchema = z.object({
  planId: getUuidSchema("Subscription Plan ID"),
  ownerId: getUuidSchema("Owner ID"),
  paymentCycle: subscriptionPaymentCycleSchema,
  endDate: z
    .string()
    .datetime({
      offset: true,
      message: "Invalid end date format. Use ISO 8601 UTC timestamp string",
    })
    .transform(val => new Date(val)),
});

export class CreateSubscriptionDto extends createZodDto(
  createSubscriptionBaseSchema,
) {}
