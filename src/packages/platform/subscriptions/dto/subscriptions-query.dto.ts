import { z } from "zod";
import { createZodDto } from "nestjs-zod";
import { listAllEntitiesQuerySchema } from "../../../../shared/schema/zod-common.schema.js";
import { subscriptionStatusSchema } from "./create-subscription.dto.js";

export const listSubscriptionsQuerySchema = listAllEntitiesQuerySchema.extend({
  status: subscriptionStatusSchema.optional(),
  ownerId: z.string().uuid().optional(),
});

export class ListSubscriptionsQueryDto extends createZodDto(
  listSubscriptionsQuerySchema,
) {}
