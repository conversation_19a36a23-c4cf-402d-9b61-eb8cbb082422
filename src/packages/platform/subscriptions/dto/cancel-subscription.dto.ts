import { z } from "zod";
import { createZodDto } from "nestjs-zod";

export const cancelSubscriptionSchema = z.object({
  reason: z
    .string()
    .min(10, { message: "Cancellation reason must be at least 10 characters" })
    .max(500, { message: "Cancellation reason cannot exceed 500 characters" })
    .optional(),
});

export class CancelSubscriptionDto extends createZodDto(cancelSubscriptionSchema) {}
