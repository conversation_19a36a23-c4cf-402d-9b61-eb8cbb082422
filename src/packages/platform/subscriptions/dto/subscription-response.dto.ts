import { z } from "zod";
import { createZodDto } from "nestjs-zod";
import { getUuidSchema } from "../../../../shared/schema/zod-common.schema.js";
import { createSingleResourceResponseSchema, createPaginatedResourceResponseSchema } from "../../../../shared/schema/api-response.schema.js";
import { subscriptionStatusSchema, subscriptionPaymentCycleSchema } from "./create-subscription.dto.js";

// Subscription plan info for response
export const subscriptionPlanInfoSchema = z.object({
  id: getUuidSchema("Subscription Plan ID"),
  title: z.string().describe("Title of the subscription plan"),
  description: z.string().nullable().describe("Description of the subscription plan"),
  price: z.number().describe("Price of the subscription plan"),
  setupCharges: z.number().describe("Setup charges for the subscription plan"),
  branches: z.number().describe("Number of branches allowed"),
  students: z.number().describe("Number of students allowed"),
  features: z.array(z.string()).describe("List of features included in the plan"),
});

// Owner info for response
export const ownerInfoSchema = z.object({
  id: getUuidSchema("Owner ID"),
  name: z.string().describe("Name of the owner"),
  email: z.string().email().describe("Email of the owner"),
});

export const subscriptionResponseSchema = z.object({
  id: getUuidSchema("Subscription ID"),
  plan: subscriptionPlanInfoSchema,
  owner: ownerInfoSchema,
  status: subscriptionStatusSchema,
  paymentCycle: subscriptionPaymentCycleSchema,
  startDate: z.date().describe("Subscription start date"),
  endDate: z.date().describe("Subscription end date"),
  lastPaymentDate: z.date().describe("Last payment date"),
  cancellationDate: z.date().nullable().describe("Cancellation date if cancelled"),
  createdAt: z.date().describe("Date when subscription was created"),
});

export class SubscriptionResponseDto extends createZodDto(
  createSingleResourceResponseSchema(subscriptionResponseSchema),
) {}

export const listSubscriptionsResponseSchema = z.object({
  items: z.array(subscriptionResponseSchema),
  total: z.number().describe("Total number of subscriptions"),
});

export class ListSubscriptionsResponseDto extends createZodDto(
  createPaginatedResourceResponseSchema(subscriptionResponseSchema),
) {}
