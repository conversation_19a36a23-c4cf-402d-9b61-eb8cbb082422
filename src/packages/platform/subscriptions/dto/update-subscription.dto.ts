import { z } from "zod";
import { createZodDto } from "nestjs-zod";
import { getUuidSchema } from "../../../../shared/schema/zod-common.schema.js";
import { subscriptionStatusSchema, subscriptionPaymentCycleSchema } from "./create-subscription.dto.js";

export const updateSubscriptionSchema = z.object({
  planId: getUuidSchema("Subscription Plan ID").optional(),
  status: subscriptionStatusSchema.optional(),
  paymentCycle: subscriptionPaymentCycleSchema.optional(),
  endDate: z
    .string()
    .datetime({
      offset: true,
      message: "Invalid end date format. Use ISO 8601 UTC timestamp string",
    })
    .transform(val => new Date(val))
    .optional(),
  lastPaymentDate: z
    .string()
    .datetime({
      offset: true,
      message: "Invalid last payment date format. Use ISO 8601 UTC timestamp string",
    })
    .transform(val => new Date(val))
    .optional(),
});

export class UpdateSubscriptionDto extends createZodDto(updateSubscriptionSchema) {}
