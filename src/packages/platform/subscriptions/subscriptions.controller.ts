import {
  Controller,
  Get,
  Post,
  Patch,
  Query,
  Body,
  Param,
  ParseUUIDPipe,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { ZodSerializerDto } from "nestjs-zod";
import { SubscriptionsService } from "./subscriptions.service.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { User } from "../../../shared/decorators/user.decorator.js";
import type { AuthenticatedUser } from "../../../core/auth/type/auth.type.js";
import { CreateSubscriptionDto } from "./dto/create-subscription.dto.js";
import { UpdateSubscriptionDto } from "./dto/update-subscription.dto.js";
import { CancelSubscriptionDto } from "./dto/cancel-subscription.dto.js";
import { ListSubscriptionsQueryDto } from "./dto/subscriptions-query.dto.js";
import {
  SubscriptionResponseDto,
  ListSubscriptionsResponseDto,
} from "./dto/subscription-response.dto.js";
import {
  ApiDocCreateSubscription,
  ApiDocUpdateSubscription,
  ApiDocListSubscriptions,
  ApiDocGetSubscriptionById,
  ApiDocGetOwnerSubscription,
  ApiDocCancelSubscription,
} from "./docs/subscriptions.docs.js";

@Controller("subscriptions")
@ApiTags("Subscriptions")
export class SubscriptionsController {
  public constructor(
    private readonly subscriptionsService: SubscriptionsService,
  ) {}

  @Roles(["PLATFORM_ADMIN"])
  @Post()
  @ZodSerializerDto(SubscriptionResponseDto)
  @ApiDocCreateSubscription()
  public async create(@Body() createSubscriptionDto: CreateSubscriptionDto) {
    return await this.subscriptionsService.create({
      ...createSubscriptionDto,
      status: "ACTIVE",
      startDate: new Date(),
      lastPaymentDate: new Date(),
    });
  }

  @Roles(["PLATFORM_ADMIN"])
  @Get()
  @ZodSerializerDto(ListSubscriptionsResponseDto)
  @ApiDocListSubscriptions()
  public async getAll(@Query() query: ListSubscriptionsQueryDto) {
    return await this.subscriptionsService.findAll(query);
  }

  @Roles(["PLATFORM_ADMIN"])
  @Get(":subscriptionId")
  @ZodSerializerDto(SubscriptionResponseDto)
  @ApiDocGetSubscriptionById()
  public async getById(
    @Param("subscriptionId", ParseUUIDPipe) subscriptionId: string,
  ) {
    return await this.subscriptionsService.findByIdOrThrow(subscriptionId);
  }

  @Roles(["INSTITUTE_OWNER"])
  @Get("/me")
  @ZodSerializerDto(SubscriptionResponseDto)
  @ApiDocGetOwnerSubscription()
  public async getOwnerSubscription(@User() user: AuthenticatedUser) {
    return await this.subscriptionsService.findByOwnerIdOrThrow(user.id);
  }

  @Roles(["PLATFORM_ADMIN"])
  @Patch(":subscriptionId")
  @ZodSerializerDto(SubscriptionResponseDto)
  @ApiDocUpdateSubscription()
  public async update(
    @Param("subscriptionId", ParseUUIDPipe) subscriptionId: string,
    @Body() updateSubscriptionDto: UpdateSubscriptionDto,
  ) {
    return await this.subscriptionsService.update(
      subscriptionId,
      updateSubscriptionDto,
    );
  }

  @Roles(["PLATFORM_ADMIN"])
  @Patch(":subscriptionId/cancel")
  @ZodSerializerDto(SubscriptionResponseDto)
  @ApiDocCancelSubscription()
  public async cancel(
    @Param("subscriptionId", ParseUUIDPipe) subscriptionId: string,
    @Body() cancelSubscriptionDto: CancelSubscriptionDto,
  ) {
    return await this.subscriptionsService.cancel(
      subscriptionId,
      cancelSubscriptionDto.reason,
    );
  }
}
