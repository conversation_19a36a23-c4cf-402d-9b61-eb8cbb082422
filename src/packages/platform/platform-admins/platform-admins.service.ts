import { Injectable, Logger } from "@nestjs/common";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../db/type.js";
import { platformAdminTable, usersTable } from "../../../db/schemas/index.js";
import { eq, getTableColumns } from "drizzle-orm";

@Injectable()
export class PlatformAdminsService {
  private logger = new Logger(PlatformAdminsService.name);

  constructor(@InjectDrizzle() private readonly db: Database) {}

  async findByEmailIncludePassword(email: string) {
    try {
      return await this.db
        .select({
          ...getTableColumns(platformAdminTable),
          email: usersTable.email,
          name: usersTable.name,
          password: usersTable.password,
          roleId: usersTable.roleId,
        })
        .from(platformAdminTable)
        .innerJoin(usersTable, eq(usersTable.id, platformAdminTable.userId))
        .where(eq(usersTable.email, email))
        .then(res => res[0]);
    } catch (error: unknown) {
      this.logger.error("Failed to get platform admin by email", error);
      throw error;
    }
  }
}
