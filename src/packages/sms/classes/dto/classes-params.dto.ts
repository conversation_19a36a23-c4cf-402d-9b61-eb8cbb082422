import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import { academicSessionIdParamSchema } from "../../../../shared/dto/param.dto.js";
import { getUuidSchema } from "../../../../shared/schema/zod-common.schema.js";

export class ClassParamsDto extends createZodDto(
  z.object({ branchId: z.string().nonempty() }),
) {}

export class AcademicSessionClassParamsDto extends createZodDto(
  academicSessionIdParamSchema.extend({ classId: getUuidSchema("Class ID") }),
) {}

export class ClassIdParamDto extends createZodDto(
  z.object({ classId: getUuidSchema("Class ID") }),
) {}
