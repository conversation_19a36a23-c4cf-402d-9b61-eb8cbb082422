import { z } from "zod";
import {
  nameSchema,
  getUuidSchema,
} from "../../../../shared/schema/zod-common.schema.js";
import { createZodDto } from "nestjs-zod";
import {
  classSectionResponseSchema,
  createClassSectionSchema,
} from "../../sections/dto/class-sections.dto.js";

const baseClassSchema = z.object({
  name: nameSchema,
  maximumStudents: z
    .number({ message: "Maximum students is required" })
    .positive()
    .max(1000, { message: "Maximum students cannot exceed 1000" }),
  feePerMonth: z
    .number({ message: "Fees per month is required" })
    .positive()
    .max(1_000_000, {
      message: "Fees per month cannot exceed 1,000,000",
    }),
  isActive: z.boolean().default(true),
  sections: z
    .array(createClassSectionSchema)
    .min(1, { message: "At least one section is required" }),
});

// -------------- Create-Class-Dto -------------->
export const createClassSchema = baseClassSchema;
export class CreateClassDto extends createZodDto(createClassSchema) {}

// -------------- Update-Class-Dto -------------->
export const updateClassSchema = baseClassSchema
  .omit({
    sections: true,
  })
  .extend({
    sections: z.array(
      createClassSectionSchema.extend({ id: getUuidSchema("Section ID") }),
    ),
  })
  .partial();
export class UpdateClassDto extends createZodDto(updateClassSchema) {}

// -------------- Class-Response-Dto -------------->
export const classResponseSchema = baseClassSchema
  .omit({
    sections: true,
  })
  .extend({
    id: getUuidSchema("Class ID"),
    sections: z.array(classSectionResponseSchema),
    createdAt: z.date(),
  });

export class ClassResponseDto extends createZodDto(classResponseSchema) {}
