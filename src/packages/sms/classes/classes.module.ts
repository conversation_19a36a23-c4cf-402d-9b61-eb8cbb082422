import { <PERSON>du<PERSON> } from "@nestjs/common";
import { ClassesController } from "./classes.controller.js";
import { ClassesService } from "./classes.service.js";
import { AcademicSessionsModule } from "../academic-sessions/academic-sessions.module.js";
import { ClassSectionsModule } from "../sections/class-sections.module.js";

@Module({
  imports: [AcademicSessionsModule, ClassSectionsModule],
  controllers: [ClassesController],
  providers: [ClassesService],
  exports: [ClassesService],
})
export class ClassesModule {}
