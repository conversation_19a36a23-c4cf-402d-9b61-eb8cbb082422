import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import {
  Class,
  ClassResponse,
  CreateClassPayload,
  UpdateClassPayload,
} from "./types/classes.types.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertOrUpdateException,
} from "../../../utils/pg-utils.js";
import {
  EntityId,
  ListAllEntitiesQueryOptions,
  ServiceOptions,
} from "../../../shared/types/shared.types.js";
import { ClassSectionsService } from "../sections/class-sections.service.js";
import { ModuleRef } from "@nestjs/core";
import { OnModuleInit } from "@nestjs/common";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../db/type.js";
import { classTable } from "../../../db/schemas/academic.schema.js";
import { and, desc, eq, SQL, sql } from "drizzle-orm";
import { classSectionTable } from "../../../db/schemas/education.schema.js";
import { enrollmentTable } from "../../../db/schemas/student.schema.js";
import { usersTable } from "../../../db/schemas/auth.schema.js";

@Injectable()
export class ClassesService implements OnModuleInit {
  private readonly logger = new Logger(ClassesService.name);

  private classSectionsService: ClassSectionsService;

  public constructor(
    @InjectDrizzle() private readonly db: Database,
    private readonly moduleRef: ModuleRef,
  ) {}

  public onModuleInit() {
    this.classSectionsService = this.moduleRef.get(ClassSectionsService, {
      strict: false,
    });
  }

  public async create(
    createClassPayload: CreateClassPayload,
  ): Promise<EntityId> {
    try {
      const { sections, ...classPayload } = createClassPayload;

      return await this.db.transaction(async trx => {
        const newClass = await executeQueryTakeFirstOrThrow(
          trx
            .insert(classTable)
            .values(classPayload)
            .returning({ id: classTable.id }),
        );

        const sectionsPayload = sections.map(section => ({
          ...section,
          classId: newClass.id,
        }));

        await this.classSectionsService.createMany(sectionsPayload, {
          pgTrx: trx,
        });
        return newClass;
      });
    } catch (error: unknown) {
      this.logger.error("Failed to create class", error);
      handleDatabaseInsertOrUpdateException(error, {
        resource: "classes",
        logger: this.logger,
      });
    }
  }

  public async update(
    classId: Class["id"],
    updateClassPayload: UpdateClassPayload,
  ): Promise<EntityId> {
    try {
      const existingClass = await this.findById(classId);
      if (!existingClass) {
        throw new NotFoundException(`Class with id: ${classId} does not found`);
      }

      const { sections, ...classPayload } = updateClassPayload;
      return await this.db.transaction(async trx => {
        const { id: updatedClassId } = await executeQueryTakeFirstOrThrow(
          trx
            .update(classTable)
            .set(classPayload)
            .where(eq(classTable.id, classId))
            .returning({ id: classTable.id }),
        );

        if (sections) {
          for (const section of sections) {
            await this.classSectionsService.createOrUpdate(
              { ...section, classId: updatedClassId },
              { pgTrx: trx },
            );
          }
        }
        return { id: updatedClassId };
      });
    } catch (error) {
      this.logger.error("Failed to update class", error);
      handleDatabaseInsertOrUpdateException(error, {
        resource: "classes",
        logger: this.logger,
      });
    }
  }

  public async findAllByAcademicSessionId(
    sessionId: string,
    queryOptions: ListAllEntitiesQueryOptions,
  ) {
    try {
      const [items, total] = await Promise.all([
        await this.buildSelectQuery(
          this.buildQueryFilters({ academicSessionId: sessionId }),
        )
          .limit(queryOptions.limit)
          .offset(queryOptions.offset)
          .execute(),

        await this.db.$count(
          classTable,
          eq(classTable.academicSessionId, sessionId),
        ),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all classes", error);
      throw error;
    }
  }

  public async findClassOrThrow(classId: string, options?: ServiceOptions) {
    let existingClass: ClassResponse | undefined;
    try {
      existingClass = await this.findById(classId, options);
    } catch (error: unknown) {
      this.logger.error("Failed to verify class exists", error);
      throw error;
    }
    if (!existingClass) {
      throw new NotFoundException(`Class with id: ${classId} not found`);
    }
    return existingClass;
  }

  public async findFirst(
    criteria: Partial<Pick<Class, "id">>,
    options?: ServiceOptions,
  ) {
    try {
      return await this.buildSelectQuery(
        this.buildQueryFilters({ id: criteria.id }),
        options,
      )
        .execute()
        .then(res => res[0]);
    } catch (error: unknown) {
      this.logger.error("Failed to find class", error);
      throw error;
    }
  }

  public async findById(
    id: Class["id"],
    options?: ServiceOptions,
  ): Promise<ClassResponse | undefined> {
    try {
      return await this.findFirst({ id }, options);
    } catch (error: unknown) {
      this.logger.error("Failed to find class by id", error);
      throw error;
    }
  }

  public async verifyClassExists(classId: string, options?: ServiceOptions) {
    let existingClass: ClassResponse | undefined;
    try {
      existingClass = await this.findById(classId, options);
    } catch (error: unknown) {
      this.logger.error("Failed to verify class exists", error);
      throw error;
    }

    if (!existingClass) {
      throw new NotFoundException(`Class with id: ${classId} not found`);
    }
  }

  private buildSelectQuery(filters: SQL[], options?: ServiceOptions) {
    const dbClient = options?.pgTrx ?? this.db;
    return dbClient
      .select({
        id: classTable.id,
        name: classTable.name,
        feePerMonth: classTable.feePerMonth,
        maximumStudents: classTable.maximumStudents,
        isActive: classTable.isActive,
        createdAt: classTable.createdAt,
        sections: sql<ClassResponse["sections"]>`json_agg(json_build_object(
            'id', ${classSectionTable.id},
            'name', ${classSectionTable.name},
            'createdAt', ${classSectionTable.createdAt},
            'isActive', ${classSectionTable.isActive},
            'totalStudents', (
              SELECT COUNT(*)::int
              FROM ${enrollmentTable}
              WHERE ${enrollmentTable.classSectionId} = ${classSectionTable.id}
            ),
            'classTeacher',json_build_object(
                  'id', ${usersTable.id},
                  'name', ${usersTable.name},
                  'photo', ${usersTable.photo}
               )
          ))`,
      })
      .from(classTable)
      .innerJoin(
        classSectionTable,
        eq(classSectionTable.classId, classTable.id),
      )
      .innerJoin(
        usersTable,
        eq(usersTable.id, classSectionTable.classTeacherId),
      )
      .where(and(...filters))
      .orderBy(desc(classTable.createdAt))
      .groupBy(classTable.id);
  }

  private buildQueryFilters(
    filterOptions: Partial<Pick<Class, "academicSessionId" | "id">>,
  ) {
    const filters: SQL[] = [];
    if (filterOptions.academicSessionId) {
      filters.push(
        eq(classTable.academicSessionId, filterOptions.academicSessionId),
      );
    }

    if (filterOptions.id) {
      filters.push(eq(classTable.id, filterOptions.id));
    }

    return filters;
  }
}
