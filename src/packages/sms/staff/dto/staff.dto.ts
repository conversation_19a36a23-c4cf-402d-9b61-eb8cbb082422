import { z } from "zod";
import {
  addressSchema,
  cnicSchema,
  genderSchema,
  imageUrlSchema,
  nameSchema,
  passwordSchema,
  phoneNumberSchema,
  safePositiveNumberSchema,
} from "../../../../shared/schema/zod-common.schema.js";
import { createZodDto } from "nestjs-zod";
import { sectionSubjectAssignmentResponseSchema } from "../../sections-subject-assignment/dto/section-subjects.dto.js";
import { classSectionResponseSchema } from "../../sections/dto/class-sections.dto.js";
import { branchResponseSchema } from "../../branches/dto/branches.dto.js";

export const staffTypeSchema = z.enum(
  ["TEACHER", "BRANCH_ADMIN", "ACCOUNTANT", "SUPPORT_STAFF"],
  {
    message: "Role must be one of 'TEACHER', 'BRANCH_ADMIN', or 'ACCOUNTANT'.",
  },
);

const baseStaffSchema = z.object({
  name: nameSchema,
  email: z
    .string()
    .email({ message: "Invalid email address" })
    .max(100, { message: "Email must be less than 100 characters" }),
  phone: phoneNumberSchema,
  address: addressSchema,
  gender: genderSchema,
  photo: imageUrlSchema.nullable().optional(),
  password: passwordSchema.nullable().optional(),
  designation: z
    .string()
    .nonempty()
    .max(100, { message: "Designation must be less than 100 characters" }),
  department: z.enum(["ACADEMIC", "ADMINISTRATION", "SUPPORT"], {
    message:
      "Department must be one of 'ACADEMIC', 'ADMINISTRATION', or 'SUPPORT'.",
  }),
  type: staffTypeSchema,
  salary: safePositiveNumberSchema,
  cnic: cnicSchema,
});

// -------------------- Create-Staff-Dto -------------------->
export const createStaffSchema = baseStaffSchema
  .refine(
    data => {
      // Check if the role is valid for the department
      if (data.department === "ACADEMIC" && data.type !== "TEACHER") {
        return false;
      }

      if (
        data.department === "ADMINISTRATION" &&
        data.type !== "BRANCH_ADMIN" &&
        data.type !== "ACCOUNTANT"
      ) {
        return false;
      }

      if (data.department === "SUPPORT" && data.type !== "SUPPORT_STAFF") {
        return false;
      }

      return true;
    },
    {
      message: "Invalid type for the selected department.",
      path: ["type"],
    },
  )
  .refine(
    data => {
      if (data.department !== "SUPPORT" && !data.password) {
        return false;
      }
      return true;
    },
    {
      message: "Password is required for non-support staff.",
      path: ["password"],
    },
  );

export class CreateStaffDto extends createZodDto(createStaffSchema) {}

// -------------------- Update-Staff-Dto -------------------->
export const updateStaffSchema = baseStaffSchema
  .omit({
    department: true,
    type: true,
    email: true,
  })
  .partial();
export class UpdateStaffDto extends createZodDto(updateStaffSchema) {}

// -------------------- Get-Staff-Dto -------------------->
export const staffProfileSchema = z.object({
  id: z.string(),
  ...baseStaffSchema.omit({ password: true }).shape,
  createdAt: z.date(),
  branchId: z.string(),
});

export class StaffProfileResponseDto extends createZodDto(staffProfileSchema) {}

// -------------------- Teacher-Profile-Dto -------------------->
/**
 * Teacher profile schema that extends staff profile with teaching assignments
 * Includes class teacher assignments and subject teaching assignments
 */
export const teacherProfileSchema = staffProfileSchema.extend({
  classTeacherOf: z
    .array(classSectionResponseSchema.omit({ classTeacher: true }))
    .describe("List of class sections where this teacher is the class teacher"),

  subjectTeacherOf: z
    .array(
      sectionSubjectAssignmentResponseSchema.omit({ subjectTeacher: true }),
    )
    .describe("List of subject teaching assignments for this teacher"),
});

/**
 * Basic teacher info schema for simple teacher references
 * Used when only basic teacher information is needed (id and name)
 */
export const basicTeacherInfoSchema = z.object({
  id: z.string().describe("Teacher's unique identifier"),
  name: z.string().describe("Teacher's full name"),
});

/**
 * Basic Teacher Info Response DTO
 * Used for API responses when returning minimal teacher information
 * Contains only essential teacher identification data
 */
export class BasicTeacherInfoResponseDto extends createZodDto(
  basicTeacherInfoSchema,
) {}

/**
 * Teacher Profile Response DTO
 * Used for API responses when returning teacher profile data
 * Includes all staff information plus teaching assignments
 */
export class TeacherProfileResponseDto extends createZodDto(
  teacherProfileSchema,
) {}

// -------------------- Branch-Admin-Profile-Dto -------------------->
export const branchAdminProfileSchema = staffProfileSchema
  .omit({ branchId: true })
  .extend({
    branch: branchResponseSchema,
  });
export class BranchAdminProfileResponseDto extends createZodDto(
  branchAdminProfileSchema,
) {}
