import { ConflictException, Injectable, Logger } from "@nestjs/common";
import { CreateGuardianPayload, Guardian } from "./types/guardian.types.js";
import { omitKeysFromObject } from "../../../utils/object-utils.js";
import { UsersService } from "../../../core/users/users.service.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertException,
} from "../../../utils/pg-utils.js";
import { RolesService } from "../../../core/roles/roles.service.js";
import {
  EntityId,
  ServiceOptions,
} from "../../../shared/types/shared.types.js";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../db/type.js";
import { eq } from "drizzle-orm";
import { guardianTable } from "../../../db/schemas/student.schema.js";

@Injectable()
export class GuardiansService {
  private readonly logger = new Logger(GuardiansService.name);

  public constructor(
    @InjectDrizzle() private readonly db: Database,
    private readonly userService: UsersService,
    private readonly rolesService: RolesService,
  ) {}

  public async findById(id: Guardian["userId"]) {
    try {
      return await this.db.query.guardianTable.findFirst({
        where: eq(guardianTable.userId, id),
      });
    } catch (error: unknown) {
      this.logger.error("Failed to find guardian by user id", error);
      throw error;
    }
  }

  public async create(
    createGuardianPayload: CreateGuardianPayload,
    options: ServiceOptions,
  ): Promise<EntityId> {
    try {
      const userInfo = omitKeysFromObject(createGuardianPayload, ["relation"]);

      return await this.db.transaction(async guardianTrx => {
        const activeTrx = options.pgTrx ?? guardianTrx;

        const guardianRole =
          await this.rolesService.findByNameOrCreate("GUARDIAN");

        const { id: userAccountId } = await this.userService.create(
          {
            ...userInfo,
            roleId: guardianRole.id,
          },
          {
            pgTrx: activeTrx,
          },
        );

        return await executeQueryTakeFirstOrThrow(
          activeTrx
            .insert(guardianTable)
            .values({
              relation: createGuardianPayload.relation,
              userId: userAccountId,
            })
            .returning({ id: guardianTable.userId }),
        );
      });
    } catch (error: unknown) {
      this.logger.error("Failed to create guardian");

      if (error instanceof ConflictException) {
        throw new ConflictException("Guardian with same email already exists");
      }

      handleDatabaseInsertException(error, {
        resource: "guardian",
        logger: this.logger,
      });
    }
  }
}
