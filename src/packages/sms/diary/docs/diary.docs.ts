import { Api<PERSON><PERSON>ation, ApiParam, ApiQ<PERSON>y, ApiResponse } from "@nestjs/swagger";
import {
  useCommonCreateResourceApiResponses,
  useCommonListResourceDocs,
} from "../../../../docs/shared/api-responses.js";
import { DiaryResponseDto, ListDiaryResponseDto } from "../dto/diary.dto.js";

export function ApiDocCreateDiary() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Create Diary Entry",
      description: "Creates a new diary entry for a specific class section",
    }),
    ApiResponse({
      status: 201,
      type: DiaryResponseDto,
      description: "Returns the  newly created diary",
    }),
  );
}

export function ApiDocGetAllDiaries() {
  return useCommonListResourceDocs(
    ApiOperation({
      summary: "Get All Diary Entries",
      description: "Retrieves all diary entries for a specific class section",
    }),

    ApiParam({
      name: "sectionId",
      required: true,
      description: "Unique identifier of the class section",
      type: String,
      example: "123e4567-e89b-12d3-a456-426614174000",
    }),

    ApiParam({
      name: "subjectId",
      required: false,
      description: "Unique identifier of the subject",
      type: String,
      example: "123e4567-e89b-12d3-a456-426614174000",
    }),

    ApiQuery({
      name: "date",
      required: false,
      description: "Filter by specific date (YYYY-MM-DD)",
      type: String,
      example: "2024-01-15",
    }),

    ApiQuery({
      name: "month",
      required: false,
      description: "Filter by specific month (YYYY-MM)",
      type: String,
      example: "2024-01",
    }),

    ApiQuery({
      name: "from",
      required: false,
      description: "Filter from date (YYYY-MM-DD)",
      type: String,
      example: "2024-01-15",
    }),

    ApiQuery({
      name: "to",
      required: false,
      description: "Filter to date (YYYY-MM-DD)",
      type: String,
      example: "2024-01-31",
    }),

    ApiResponse({
      status: 200,
      description: "Successfully retrieved diary entries",
      type: ListDiaryResponseDto,
    }),
  );
}

export function ApiDocUpdateDiary() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Update Diary Entry",
      description: "Updates an existing diary entry",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the updated diary entry",
      type: DiaryResponseDto,
    }),
  );
}
