import {
  ForbiddenException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import { gte, lte, sql, and, desc, eq, SQL } from "drizzle-orm";
import {
  Diary,
  DiaryResponse,
  DiaryUpdate,
  ListDiariesQueryOptions,
  NewDiary,
} from "./types/diary.type.js";
import {
  getCurrentDateInPgFormat,
  handleDatabaseInsertOrUpdateException,
  executeQueryTakeFirstOrThrow,
} from "../../../utils/pg-utils.js";
import { PaginatedApiResponse } from "../../../shared/types/shared.types.js";
import type { Database } from "../../../db/type.js";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import {
  classSectionTable,
  classTable,
  diaryTable,
  enrollmentTable,
  staffTable,
  subjectTable,
  usersTable,
} from "../../../db/schemas/index.js";
import { StaffService } from "../staff/staff.service.js";
import { SectionSubjectsService } from "../sections-subject-assignment/section-subjects.service.js";
import { paginationOptions } from "../../../shared/constants/api-request.constants.js";

@Injectable()
export class DiaryService {
  private readonly logger = new Logger(DiaryService.name);

  constructor(
    @InjectDrizzle() private readonly db: Database,
    private readonly staffService: StaffService,
    private readonly sectionSubjectsService: SectionSubjectsService,
  ) {}

  public async create(newDiaryData: NewDiary): Promise<DiaryResponse> {
    try {
      await this.verifyTeacherHasGivenAccess(newDiaryData.teacherId, {
        classSectionId: newDiaryData.classSectionId,
        subjectId: newDiaryData.subjectId,
      });

      const newDiary = await executeQueryTakeFirstOrThrow(
        this.db
          .insert(diaryTable)
          .values(newDiaryData)
          .returning({ id: diaryTable.id }),
      );
      return await this.findByIdOrThrow(newDiary.id);
    } catch (error: unknown) {
      this.logger.error("Failed to create diary", error);
      handleDatabaseInsertOrUpdateException(error, {
        resource: "diary",
        logger: this.logger,
      });
    }
  }

  public async update(
    id: string,
    teacherId: string,
    updateDiaryData: DiaryUpdate,
  ): Promise<DiaryResponse> {
    try {
      const existingDiary = await this.findById(id);

      if (!existingDiary) {
        throw new NotFoundException(`Diary with id: ${id} not found`);
      }

      await this.verifyTeacherHasGivenAccess(teacherId, {
        classSectionId: existingDiary.classSection.id,
        subjectId: existingDiary.subject.id,
      });

      const updatedDiary = await executeQueryTakeFirstOrThrow(
        this.db
          .update(diaryTable)
          .set(updateDiaryData)
          .where(eq(diaryTable.id, id))
          .returning({ id: diaryTable.id }),
      );

      return await this.findByIdOrThrow(updatedDiary.id);
    } catch (error: unknown) {
      this.logger.error("Failed to update diary");
      handleDatabaseInsertOrUpdateException(error, {
        resource: "diary",
        logger: this.logger,
      });
    }
  }

  public async findById(id: Diary["id"]) {
    try {
      const res = await this.find({ id });
      return res.items[0];
    } catch (error: unknown) {
      this.logger.error("Failed to find diary by id", error);
      throw error;
    }
  }

  private async findByIdOrThrow(id: string, message = "Failed to find diary") {
    const response = await this.find({ id });
    const existingDiary = response.items[0];
    if (!existingDiary) {
      throw new InternalServerErrorException(message);
    }
    return existingDiary;
  }

  private async verifyTeacherHasGivenAccess(
    teacherId: string,
    {
      classSectionId,
      subjectId,
    }: Pick<NewDiary, "classSectionId" | "subjectId">,
  ) {
    try {
      const isClassTeacher = await this.staffService.checkTeacherIsClassTeacher(
        classSectionId,
        teacherId,
      );
      if (isClassTeacher) {
        return;
      }

      const isSubjectTeacher =
        await this.staffService.checkTeacherIsSubjectTeacher(teacherId, {
          classSectionId,
          subjectId,
        });

      if (!isSubjectTeacher) {
        throw new ForbiddenException("You do not have access to this resource");
      }
    } catch (error) {
      if (!(error instanceof HttpException)) {
        this.logger.error("Failed to verify teacher has access", error);
      }
      throw error;
    }
  }

  /**
   * Find all diary entries for a specific class section with optional filtering
   * Supports filtering by date, month, date range, and subject
   */
  public async findAllBySectionId(
    classSectionId: string,
    queryOptions: ListDiariesQueryOptions,
  ): Promise<PaginatedApiResponse<DiaryResponse>> {
    try {
      return await this.find({ classSectionId }, queryOptions);
    } catch (error) {
      this.logger.error("Failed to find diaries by section id", error);
      throw error;
    }
  }

  public async find(
    criteria: Partial<Diary>,
    queryOptions?: ListDiariesQueryOptions,
  ): Promise<PaginatedApiResponse<DiaryResponse>> {
    const filters: SQL[] = [];

    if (criteria.id) {
      filters.push(eq(diaryTable.id, criteria.id));
    }

    if (criteria.classSectionId) {
      filters.push(eq(diaryTable.classSectionId, criteria.classSectionId));
    }

    if (criteria.subjectId) {
      filters.push(eq(diaryTable.subjectId, criteria.subjectId));
    }

    if (criteria.teacherId) {
      filters.push(eq(diaryTable.teacherId, criteria.teacherId));
    }

    if (criteria.date) {
      filters.push(eq(diaryTable.date, criteria.date));
    }

    if (criteria.createdAt) {
      filters.push(eq(diaryTable.createdAt, criteria.createdAt));
    }

    if (queryOptions?.subjectId) {
      filters.push(eq(diaryTable.subjectId, queryOptions.subjectId));
    }

    if (queryOptions?.date) {
      filters.push(eq(diaryTable.date, queryOptions.date));
    }

    if (queryOptions?.month) {
      const [year, month] = queryOptions.month.split("-");
      if (year && month) {
        filters.push(
          eq(sql`EXTRACT(YEAR FROM ${diaryTable.date})`, parseInt(year)),
          eq(sql`EXTRACT(MONTH FROM ${diaryTable.date})`, parseInt(month)),
        );
      }
    }

    if (queryOptions?.from && queryOptions.to) {
      filters.push(
        gte(diaryTable.date, queryOptions.from),
        lte(diaryTable.date, queryOptions.to),
      );
    }

    if (!queryOptions?.date) {
      filters.push(eq(diaryTable.date, getCurrentDateInPgFormat()));
    }

    try {
      const [items, total] = await Promise.all([
        this.db
          .select({
            id: diaryTable.id,
            content: diaryTable.content,
            date: diaryTable.date,
            createdAt: diaryTable.createdAt,
            subject: sql<DiaryResponse["subject"]>`json_build_object(
            'id', ${subjectTable.id},
            'name', ${subjectTable.name},
            'type', ${subjectTable.type},
            'marks', ${subjectTable.marks}
          )`,
            createdBy: sql<DiaryResponse["createdBy"]>`json_build_object(
            'id', ${usersTable.id},
            'name', ${usersTable.name},
            'photo', ${usersTable.photo}
          )`,
            classSection: sql<DiaryResponse["classSection"]>`json_build_object(
            'id', ${classSectionTable.id},
            'name', ${classSectionTable.name},
            'totalStudents', (
                SELECT COUNT(*)::int
                FROM ${enrollmentTable}
                WHERE ${enrollmentTable.classSectionId} = ${classSectionTable.id}
            ),
            'class', json_build_object(
              'id', ${classTable.id},
              'name', ${classTable.name}
            )
          )`,
          })
          .from(diaryTable)
          .innerJoin(subjectTable, eq(subjectTable.id, diaryTable.subjectId))
          .innerJoin(
            classSectionTable,
            eq(classSectionTable.id, diaryTable.classSectionId),
          )
          .innerJoin(classTable, eq(classTable.id, classSectionTable.classId))
          .innerJoin(
            staffTable,
            eq(staffTable.id, classSectionTable.classTeacherId),
          )
          .innerJoin(usersTable, eq(usersTable.id, staffTable.userId))
          .where(and(...filters))
          .offset(queryOptions?.offset ?? paginationOptions.OFFSET)
          .limit(queryOptions?.limit ?? paginationOptions.LIMIT)
          .orderBy(desc(diaryTable.createdAt))
          .execute(),

        this.db.$count(diaryTable, and(...filters)),
      ]);

      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find diaries", error);
      throw error;
    }
  }
}
