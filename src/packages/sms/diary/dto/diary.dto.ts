import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import {
  getUuidSchema,
  pgDateSchema,
  getListEntitiesByDateQuerySchema,
} from "../../../../shared/schema/zod-common.schema.js";
import {
  createPaginatedResourceResponseSchema,
  createSingleResourceResponseSchema,
} from "../../../../shared/schema/api-response.schema.js";

const diaryBaseSchema = z.object({
  subjectId: getUuidSchema("Subject ID"),
  content: z.string(),
  date: pgDateSchema,
});

// ------------------- Create-Diary-Dto ------------------->
export const createDiarySchema = diaryBaseSchema;
export class CreateDiaryDto extends createZodDto(diaryBaseSchema) {}

// ------------------- Update-Diary-Dto ------------------->
export const updateDiarySchema = diaryBaseSchema.pick({ content: true });
export class UpdateDiaryDto extends createZodDto(updateDiarySchema) {}

// ------------------- Diary-Response-Dto ------------------->
/**
 * Diary response schema with complete diary entry information
 * Includes subject details and class section information
 */
export const diaryResponseSchema = z.object({
  id: z.string().describe("Diary entry ID"),
  content: z.string().describe("Diary entry content"),
  date: z.string().describe("Date of the diary entry"),
  createdAt: z.coerce.date().describe("Creation timestamp"),
  subject: z
    .object({
      id: z.string().describe("Subject ID"),
      name: z.string().describe("Subject name"),
      type: z.enum(["THEORY", "PRACTICAL"]).describe("Subject type"),
      marks: z.number().describe("Subject marks"),
    })
    .describe("Subject information"),
  createdBy: z
    .object({
      id: z.string().describe("User ID"),
      name: z.string().describe("User name"),
      photo: z.string().nullable().optional().describe("User photo"),
    })
    .describe("Teacher information"),
  classSection: z
    .object({
      id: z.string().describe("Class section ID"),
      name: z.string().describe("Class section name"),
      totalStudents: z.number().describe("Total students in the class section"),
      class: z
        .object({
          id: z.string().describe("Class ID"),
          name: z.string().describe("Class name"),
        })
        .describe("Class information"),
    })
    .describe("Class section information"),
});

export class ListDiaryResponseDto extends createZodDto(
  createPaginatedResourceResponseSchema(diaryResponseSchema),
) {}

export class DiaryResponseDto extends createZodDto(
  createSingleResourceResponseSchema(diaryResponseSchema),
) {}

// ------------------- List-Diaries-Query-Dto ------------------->
/**
 * Query parameters for filtering diary entries
 * Supports date-based filtering and pagination
 */
export const listDiariesQuerySchema = getListEntitiesByDateQuerySchema(
  z.object({
    subjectId: getUuidSchema("Subject ID")
      .optional()
      .describe("Filter by subject ID"),
  }),
);

export class ListDiariesQueryDto extends createZodDto(listDiariesQuerySchema) {}
