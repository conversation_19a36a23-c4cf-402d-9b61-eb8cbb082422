import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { <PERSON><PERSON><PERSON>roller } from "./diary.controller.js";
import { DiaryService } from "./diary.service.js";
import { SubjectsModule } from "../subjects/subjects.module.js";
import { ClassSectionsModule } from "../sections/class-sections.module.js";
import { StaffModule } from "../staff/staff.module.js";
import { SectionSubjectsModule } from "../sections-subject-assignment/section-subjects.module.js";

@Module({
  controllers: [DiaryController],
  imports: [
    ClassSectionsModule,
    SubjectsModule,
    StaffModule,
    SectionSubjectsModule,
  ],
  providers: [DiaryService],
})
export class DiaryModule {}
