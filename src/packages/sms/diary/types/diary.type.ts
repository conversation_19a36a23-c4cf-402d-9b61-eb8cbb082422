import {
  diaryResponseSchema,
  listDiariesQuerySchema,
} from "../dto/diary.dto.js";
import { z } from "zod";
import { diaryTable } from "../../../../db/schemas/index.js";

export type Diary = typeof diaryTable.$inferSelect;
export type NewDiary = typeof diaryTable.$inferInsert;
export type DiaryUpdate = Pick<Diary, "content">;

// Response types
export type DiaryResponse = z.infer<typeof diaryResponseSchema>;

// Query types
export type ListDiariesQueryOptions = z.infer<typeof listDiariesQuerySchema>;
