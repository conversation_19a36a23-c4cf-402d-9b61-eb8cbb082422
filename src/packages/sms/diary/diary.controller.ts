import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  Patch,
  Query,
} from "@nestjs/common";
import { ApiTags, ApiExtraModels } from "@nestjs/swagger";
import {
  CreateDiaryDto,
  ListDiariesQueryDto,
  ListDiaryResponseDto,
  UpdateDiaryDto,
} from "./dto/diary.dto.js";
import { DiaryService } from "./diary.service.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import {
  ApiDocCreateDiary,
  ApiDocGetAllDiaries,
  ApiDocUpdateDiary,
} from "./docs/diary.docs.js";
import { User } from "../../../shared/decorators/user.decorator.js";
import type { AuthenticatedUser } from "../../../core/auth/type/auth.type.js";

@Controller()
@ApiTags("Diary")
@ApiExtraModels(ListDiaryResponseDto)
export class DiaryController {
  public constructor(private readonly diaryService: DiaryService) {}

  /**
   * Create a new diary entry for a specific class section
   */
  @Roles(["TEACHER"])
  @Post("sms/sections/:sectionId/diaries")
  @ApiDocCreateDiary()
  public async create(
    @Body() createDiaryDto: CreateDiaryDto,
    @Param("sectionId", ParseUUIDPipe) sectionId: string,
    @User() user: AuthenticatedUser,
  ) {
    return await this.diaryService.create({
      classSectionId: sectionId,
      teacherId: user.id,
      ...createDiaryDto,
    });
  }

  @Roles(["TEACHER"])
  @Patch("sms/diaries/:diaryId")
  @ApiDocUpdateDiary()
  public update(
    @Body() updateDiaryDto: UpdateDiaryDto,
    @Param("diaryId", ParseUUIDPipe) diaryId: string,
    @User() user: AuthenticatedUser,
  ) {
    const teacherId = user.id;
    return this.diaryService.update(diaryId, teacherId, updateDiaryDto);
  }

  /**
   * Get all diary entries for a specific class section
   * Supports filtering by date, month, date range, and subject
   */
  @Roles(["TEACHER"])
  @Get("sms/sections/:sectionId/diaries")
  @ApiDocGetAllDiaries()
  public getAllBySectionId(
    @Param("sectionId", ParseUUIDPipe) sectionId: string,
    @Query() query: ListDiariesQueryDto,
  ) {
    return this.diaryService.findAllBySectionId(sectionId, query);
  }
}
