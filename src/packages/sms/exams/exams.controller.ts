import {
  <PERSON>,
  Get,
  Post,
  Body,
  Param,
  ParseU<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "@nestjs/common";
import { ExamsService } from "./exams.service.js";
import { CreateExamDto } from "./dto/create-exam.dto.js";
import { ApiTags } from "@nestjs/swagger";
import type { AuthenticatedUser } from "../../../core/auth/type/auth.type.js";
import { User } from "../../../shared/decorators/user.decorator.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { ListExamsQueryDto } from "./dto/list-exams-query.dto.js";
import { ZodSerializerDto } from "nestjs-zod";
import { ListExamsDto } from "./dto/list-exam-response.dto.js";
import {
  ApiDocCreateExam,
  ApiDocListExams,
  ApiDocUpdateExam,
} from "./docs/exam.docs.js";
import { ExamDto } from "./dto/exam.dto.js";
import { UpdateExamDto } from "./dto/update-exam.dto.js";

@Controller("sms/academic-sessions")
@ApiTags("Exams")
export class ExamsController {
  constructor(private readonly examsService: ExamsService) {}

  @Post(":sessionId/exams")
  @Roles(["BRANCH_ADMIN"])
  @ZodSerializerDto(ExamDto)
  @ApiDocCreateExam()
  create(
    @Body() createExamDto: CreateExamDto,
    @Param("sessionId", ParseUUIDPipe) sessionId: string,
    @User() user: AuthenticatedUser,
  ) {
    return this.examsService.create({
      ...createExamDto,
      createdBy: user.id,
      academicSessionId: sessionId,
    });
  }

  @Patch(":sessionId/exams/:examId")
  @Roles(["BRANCH_ADMIN"])
  @ZodSerializerDto(ExamDto)
  @ApiDocUpdateExam()
  update(
    @Body() updateExamDto: UpdateExamDto,
    @Param("examId", ParseUUIDPipe) examId: string,
  ) {
    return this.examsService.update(examId, updateExamDto);
  }

  @Get(":sessionId/exams")
  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @ZodSerializerDto(ListExamsDto)
  @ApiDocListExams()
  findAll(
    @Param("sessionId", ParseUUIDPipe) sessionId: string,
    query: ListExamsQueryDto,
  ) {
    return this.examsService.findAll({ academicSessionId: sessionId }, query);
  }
}
