import { Injectable, Logger } from "@nestjs/common";
import {
  Exam,
  ExamUpdate,
  ListExamsQueryOptions,
  NewExam,
} from "./types/exams.type.js";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../db/type.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertOrUpdateException,
} from "../../../utils/pg-utils.js";
import { examTable } from "../../../db/schemas/index.js";
import { and, desc, eq, SQL } from "drizzle-orm";
import { paginationOptions } from "../../../shared/constants/api-request.constants.js";
import { ServiceOptions } from "../../../shared/types/shared.types.js";

@Injectable()
export class ExamsService {
  private readonly logger = new Logger(ExamsService.name);

  public constructor(@InjectDrizzle() private readonly db: Database) {}

  public async create(newExamData: NewExam): Promise<Exam> {
    try {
      return await executeQueryTakeFirstOrThrow(
        this.db.insert(examTable).values(newExamData).returning(),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to create exam", error);
      handleDatabaseInsertOrUpdateException(error, {
        resource: "exam",
      });
    }
  }

  public async findById(
    id: Exam["id"],
    serviceOptions?: ServiceOptions,
  ): Promise<Exam | undefined> {
    const dbClient = serviceOptions?.pgTrx ?? this.db;
    try {
      return await dbClient.query.examTable.findFirst({
        where: eq(examTable.id, id),
      });
    } catch (error: unknown) {
      this.logger.error("Failed to find exam", error);
      throw error;
    }
  }

  public async update(
    examId: Exam["id"],
    updateExamData: ExamUpdate,
  ): Promise<Exam> {
    try {
      return await executeQueryTakeFirstOrThrow(
        this.db
          .update(examTable)
          .set(updateExamData)
          .where(eq(examTable.id, examId))
          .returning(),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to update exam", error);
      throw error;
    }
  }

  public async findAll(
    criteria: Partial<Exam>,
    queryOptions?: ListExamsQueryOptions,
    serviceOptions?: ServiceOptions,
  ) {
    const filters: SQL[] = [];
    if (criteria.academicSessionId) {
      filters.push(eq(examTable.academicSessionId, criteria.academicSessionId));
    }
    const dbClient = serviceOptions?.pgTrx ?? this.db;
    try {
      const [items, total] = await Promise.all([
        dbClient.query.examTable.findMany({
          where: and(...filters),
          orderBy: desc(examTable.createdAt),
          limit: queryOptions?.limit ?? paginationOptions.LIMIT,
          offset: queryOptions?.offset ?? paginationOptions.OFFSET,
        }),

        this.db.$count(examTable, and(...filters)),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all exams", error);
      throw error;
    }
  }
}
