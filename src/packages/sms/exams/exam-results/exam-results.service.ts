import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  Injectable,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import { InjectDrizzle } from "../../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../../db/type.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertException,
} from "../../../../utils/pg-utils.js";
import {
  classSectionExamTable,
  classSectionTable,
  classTable,
  enrollmentTable,
  examResultTable,
  examScheduleTable,
  studentTable,
} from "../../../../db/schemas/index.js";
import { ExamResult, NewExamResult } from "./types/exam-results.type.js";
import { ExamSchedulesService } from "../exam-schedules/exam-schedules.service.js";
import { EnrollmentService } from "../../students/enrollments/enrollments.service.js";
import { StaffService } from "../../staff/staff.service.js";
import { ExamScheduleResponse } from "../exam-schedules/types/exam-schedules.type.js";
import { and, eq, sql, SQL } from "drizzle-orm";
import { getSelectClassSectionSqlQuery } from "../../../../utils/drizzle-raw-sql-queries.utils.js";

@Injectable()
export class ExamResultsService {
  private readonly logger = new Logger(ExamResultsService.name);

  public constructor(
    @InjectDrizzle() private readonly db: Database,
    private readonly examSchedulesService: ExamSchedulesService,
    private readonly staffService: StaffService,
    private readonly enrollmentsService: EnrollmentService,
  ) {}

  public async create(newExamResultData: NewExamResult) {
    try {
      const schedule = await this.examSchedulesService.findById(
        newExamResultData.examScheduleId,
      );
      if (!schedule) {
        throw new NotFoundException("Exam schedule not found");
      }

      await this.verifyStudentEnrollmentInScheduledExamClassSection(
        newExamResultData.enrollmentId,
        schedule.classSection.id,
      );

      await this.verifyCreatorHasGivenAccessToCreateScheduledExamResult(
        newExamResultData.createdBy,
        schedule,
      );

      return await executeQueryTakeFirstOrThrow(
        this.db.insert(examResultTable).values(newExamResultData).returning(),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to create exam result", error);
      handleDatabaseInsertException(error, {
        resource: "examResult",
      });
    }
  }

  public async findAll(
    criteria?: Partial<Pick<ExamResult, "examScheduleId" | "enrollmentId">>,
  ) {
    const filters: SQL[] = [];

    if (criteria?.examScheduleId) {
      filters.push(eq(examResultTable.examScheduleId, criteria.examScheduleId));
    }

    if (criteria?.enrollmentId) {
      filters.push(eq(examResultTable.enrollmentId, criteria.enrollmentId));
    }

    const {
      query: selectClassSectionQuery,
      classTeacherStaffAlias,
      classTeacherUserAlias,
    } = getSelectClassSectionSqlQuery();

    try {
      await this.db
        .select({
          id: examResultTable.id,
          marksObtained: examResultTable.marksObtained,
          remarks: examResultTable.remarks,
          isAbsent: examResultTable.isAbsent,
          student: sql`json_build_object(
            'id', ${studentTable.id},
            'name', ${studentTable.name},
            'rollNumber', ${studentTable.rollNumber}
          )`,
          classSection: selectClassSectionQuery,
        })
        .from(examResultTable)
        .innerJoin(
          examScheduleTable,
          eq(examScheduleTable.id, examResultTable.examScheduleId),
        )
        .innerJoin(
          classSectionExamTable,
          eq(classSectionExamTable.id, examScheduleTable.classSectionExamId),
        )
        .innerJoin(
          classSectionTable,
          eq(classSectionTable.id, classSectionExamTable.classSectionId),
        )
        .innerJoin(classTable, eq(classTable.id, classSectionTable.classId))
        .innerJoin(
          classTeacherStaffAlias,
          eq(classTeacherStaffAlias.id, classSectionTable.classTeacherId),
        )
        .innerJoin(
          classTeacherUserAlias,
          eq(classTeacherUserAlias.id, classTeacherStaffAlias.userId),
        )
        .innerJoin(
          enrollmentTable,
          eq(enrollmentTable.id, examResultTable.enrollmentId),
        )
        .innerJoin(studentTable, eq(studentTable.id, enrollmentTable.studentId))
        .where(and(...filters));
    } catch (error: unknown) {
      this.logger.error("Failed to find all exam results", error);
      throw error;
    }
  }

  private async verifyStudentEnrollmentInScheduledExamClassSection(
    enrollmentId: string,
    classSectionId: string,
  ) {
    try {
      const enrollment = await this.enrollmentsService.findById(enrollmentId);
      if (!enrollment) {
        throw new NotFoundException("Enrollment not found");
      }

      if (enrollment.classSection.id !== classSectionId) {
        throw new BadRequestException(
          "Student's enrollment does not belong to the class section",
        );
      }
    } catch (error: unknown) {
      if (!(error instanceof HttpException)) {
        this.logger.error(
          "Failed to verify student enrollment in class section",
          error,
        );
      }
      throw error;
    }
  }

  private async verifyCreatorHasGivenAccessToCreateScheduledExamResult(
    teacherId: string,
    examSchedule: ExamScheduleResponse,
  ) {
    try {
      const staff =
        await this.staffService.findInstitutionalStaffById(teacherId);

      if (!staff) {
        throw new NotFoundException("Staff not found");
      }

      if (staff.type === "TEACHER") {
        if (
          staff.id !== examSchedule.subject.teacher.id ||
          staff.id !== examSchedule.classSection.classTeacher.id
        ) {
          throw new ForbiddenException(
            "You are not authorized to create this result",
          );
        }
      }
    } catch (error: unknown) {
      if (!(error instanceof HttpException)) {
        this.logger.error(
          "Failed to verify teacher has access to create exam result",
          error,
        );
      }
      throw error;
    }
  }
}
