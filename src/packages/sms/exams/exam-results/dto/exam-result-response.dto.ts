import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import { getUuidSchema } from "../../../../../shared/schema/zod-common.schema.js";
import { createSingleResourceResponseSchema, createPaginatedResourceResponseSchema } from "../../../../../shared/schema/api-response.schema.js";
import { createExamResultSchema } from "./create-exam-result.dto.js";
import { classSectionResponseSchema } from "../../../sections/dto/class-sections.dto.js";

export const examResultResponseSchema = createExamResultSchema
  .omit({ enrollmentId: true })
  .extend({
    id: getUuidSchema("Exam Result ID"),
    student: z.object({
      id: getUuidSchema("Student ID"),
      name: z.string().describe("Name of the student"),
      rollNumber: z.number().describe("Roll number of the student"),
    }).describe("Student information"),
    classSection: classSectionResponseSchema,
    createdAt: z.date().describe("Date when the exam result was created"),
  });

export class ExamResultResponseDto extends createZodDto(
  createSingleResourceResponseSchema(examResultResponseSchema),
) {}

export const listExamResultsResponseSchema = z.object({
  items: z.array(examResultResponseSchema),
  total: z.number().describe("Total number of exam results"),
});

export class ListExamResultsResponseDto extends createZodDto(
  createPaginatedResourceResponseSchema(examResultResponseSchema),
) {}
