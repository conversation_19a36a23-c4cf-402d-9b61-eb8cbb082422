import {
  Body,
  Controller,
  Get,
  Post,
  Param,
  ParseUUIDPipe,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { ExamResultsService } from "./exam-results.service.js";
import { User } from "../../../../shared/decorators/user.decorator.js";
import type { AuthenticatedUser } from "../../../../core/auth/type/auth.type.js";
import { Roles } from "../../../../core/roles/decorators/roles.decorator.js";
import { CreateExamResultDto } from "./dto/create-exam-result.dto.js";

@Controller("sms/exam-schedules")
@ApiTags("Exam Results")
export class ExamResultsController {
  public constructor(private readonly examResultsService: ExamResultsService) {}

  @Roles(["BRANCH_ADMIN", "TEACHER"])
  @Post(":examScheduleId/results")
  public create(
    @Body() createExamResultDto: CreateExamResultDto,
    @Param("examScheduleId", ParseUUIDPipe) examScheduleId: string,
    @User() user: AuthenticatedUser,
  ) {
    return this.examResultsService.create({
      ...createExamResultDto,
      createdBy: user.id,
      examScheduleId,
    });
  }

  @Roles(["BRANCH_ADMIN", "TEACHER"])
  @Get(":examScheduleId/results")
  public findAll(
    @Param("examScheduleId", ParseUUIDPipe) examScheduleId: string,
  ) {
    return this.examResultsService.findAll({ examScheduleId });
  }
}
