import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  Patch,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { ZodSerializerDto } from "nestjs-zod";
import { ClassSectionExamsService } from "./class-section-exam.service.js";
import {
  AssignClassSectionExamDto,
  UpdateClassSectionExamDto,
} from "./dto/assign-section-exams.dto.js";
import { Roles } from "../../../../core/roles/decorators/roles.decorator.js";
import {
  ApiDocAssignExamToClassSections,
  ApiDocUpdateClassSectionExamAssignments,
  ApiDocListClassSectionExams,
} from "./docs/class-section-exam.docs.js";
import {
  ClassSectionExamResponseDto,
  ListClassSectionExamsResponseDto,
} from "./dto/class-section-exam-response.dto.js";

@Controller("sms/exams")
@ApiTags("Class Section Exams")
export class ClassSectionExamsController {
  constructor(
    private readonly classSectionExamsService: ClassSectionExamsService,
  ) {}

  @Roles(["BRANCH_ADMIN"])
  @Post(":examId/class-sections")
  @ZodSerializerDto(ClassSectionExamResponseDto)
  @ApiDocAssignExamToClassSections()
  public async assignExamToClassSection(
    @Body() assignClassSectionExamDto: AssignClassSectionExamDto,
    @Param("examId", ParseUUIDPipe) examId: string,
  ) {
    return this.classSectionExamsService.assignExamToClassSections(
      examId,
      assignClassSectionExamDto.classSectionIds,
    );
  }

  @Roles(["BRANCH_ADMIN"])
  @Get(":examId/class-sections")
  @ZodSerializerDto(ListClassSectionExamsResponseDto)
  @ApiDocListClassSectionExams()
  public async getAllClassSectionsForExam(
    @Param("examId", ParseUUIDPipe) examId: string,
  ) {
    return this.classSectionExamsService.findAll({ examId });
  }

  @Roles(["BRANCH_ADMIN"])
  @Patch(":examId/class-sections")
  @ZodSerializerDto(ClassSectionExamResponseDto)
  @ApiDocUpdateClassSectionExamAssignments()
  public async updateClassSectionExamAssignments(
    @Body() updateClassSectionExamDto: UpdateClassSectionExamDto,
    @Param("examId", ParseUUIDPipe) examId: string,
  ) {
    return this.classSectionExamsService.updateExamClassSectionAssignments(
      examId,
      updateClassSectionExamDto.classSectionIds,
    );
  }
}
