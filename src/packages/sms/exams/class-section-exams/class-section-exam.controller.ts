import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { ClassSectionExamsService } from "./class-section-exam.service.js";
import { AssignClassSectionExamDto } from "./dto/assign-section-exams.dto.js";
import { Roles } from "../../../../core/roles/decorators/roles.decorator.js";

@Controller("sms/exams")
@ApiTags("Class Section Exams")
export class ClassSectionExamsController {
  constructor(
    private readonly classSectionExamsService: ClassSectionExamsService,
  ) {}

  @Roles(["BRANCH_ADMIN"])
  @Post(":examId/class-sections")
  public async assignExamToClassSection(
    @Body() assignClassSectionExamDto: AssignClassSectionExamDto,
    @Param("examId", ParseUUIDPipe) examId: string,
  ) {
    return this.classSectionExamsService.assignExamToClassSections(
      examId,
      assignClassSectionExamDto.classSectionIds,
    );
  }

  @Roles(["BRANCH_ADMIN"])
  @Get(":examId/class-sections")
  public async getAllClassSectionsForExam(
    @Param("examId", ParseUUIDPipe) examId: string,
  ) {
    return this.classSectionExamsService.findAll({ examId });
  }
}
