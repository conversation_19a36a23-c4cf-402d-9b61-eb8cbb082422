import { Injectable, Logger } from "@nestjs/common";
import { InjectDrizzle } from "../../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../../db/type.js";
import { classSectionExamTable } from "../../../../db/schemas/index.js";
import { handleDatabaseInsertException } from "../../../../utils/pg-utils.js";
import { ClassSectionExam } from "./types/class-section-exam.type.js";
import { and, eq, SQL } from "drizzle-orm";

@Injectable()
export class ClassSectionExamsService {
  private readonly logger = new Logger(ClassSectionExamsService.name);

  public constructor(@InjectDrizzle() private readonly db: Database) {}

  public async assignExamToClassSections(
    examId: string,
    classSectionIds: string[],
  ) {
    try {
      return await this.db
        .insert(classSectionExamTable)
        .values(
          classSectionIds.map(id => ({
            classSectionId: id,
            examId,
          })),
        )
        .returning();
    } catch (error: unknown) {
      this.logger.error("Failed to assign exam to class sections", error);
      handleDatabaseInsertException(error, {
        resource: "classSectionExam",
      });
    }
  }

  public async findAll(criteria: Partial<Pick<ClassSectionExam, "examId">>) {
    const filters: SQL[] = [];
    if (criteria.examId) {
      filters.push(eq(classSectionExamTable.examId, criteria.examId));
    }

    try {
      const [items, total] = await Promise.all([
        this.db.query.classSectionExamTable.findMany({
          where: and(...filters),
          columns: {
            classSectionId: false,
            examId: false,
          },
          with: {
            exam: {
              columns: {
                id: true,
                name: true,
                startDate: true,
                endDate: true,
              },
            },
            classSection: {
              with: {
                class: {
                  columns: {
                    id: true,
                    name: true,
                    feePerMonth: true,
                    maximumStudents: true,
                  },
                },
              },
            },
          },
        }),

        this.db.$count(classSectionExamTable, and(...filters)),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all assignments", error);
      throw error;
    }
  }

  /**
   * Updates the class sections assigned to an exam by replacing existing assignments
   * @param examId - The exam ID
   * @param classSectionIds - Array of class section IDs to assign to the exam
   */
  public async updateExamClassSectionAssignments(
    examId: string,
    classSectionIds: string[],
  ) {
    try {
      // Use a transaction to ensure atomicity
      return await this.db.transaction(async tx => {
        // First, delete existing assignments for this exam
        await tx
          .delete(classSectionExamTable)
          .where(eq(classSectionExamTable.examId, examId));

        // Then, insert new assignments
        if (classSectionIds.length > 0) {
          const newAssignments = await tx
            .insert(classSectionExamTable)
            .values(
              classSectionIds.map(id => ({
                classSectionId: id,
                examId,
              })),
            )
            .returning();

          return newAssignments;
        }

        return [];
      });
    } catch (error: unknown) {
      this.logger.error(
        "Failed to update exam class section assignments",
        error,
      );
      handleDatabaseInsertException(error, {
        resource: "classSectionExam",
      });
    }
  }
}
