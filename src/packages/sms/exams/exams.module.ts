import { <PERSON>du<PERSON> } from "@nestjs/common";
import { ExamsService } from "./exams.service.js";
import { ExamsController } from "./exams.controller.js";
import { ClassSectionExamsModule } from "./packages/class-section-exams/class-section-exam.module.js";
import { ExamSchedulesModule } from "./packages/exam-schedules/exam-schedules.module.js";
import { ExamResultsModule } from "./packages/exam-results/exam-results.module.js";

@Module({
  imports: [ClassSectionExamsModule, ExamSchedulesModule, ExamResultsModule],
  controllers: [ExamsController],
  providers: [ExamsService],
})
export class ExamsModule {}
