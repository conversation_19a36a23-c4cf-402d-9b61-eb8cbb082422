import { Injectable, Logger } from "@nestjs/common";
import type { Database } from "../../../../db/type.js";
import { InjectDrizzle } from "../../../../shared/modules/drizzle/drizzle.decorators.js";
import {
  ExamSchedule,
  ExamScheduleResponse,
  NewExamSchedule,
} from "./types/exam-schedules.type.js";
import {
  classTable,
  sectionSubjectTable,
  subjectTable,
  classSectionExamTable,
  classSectionTable,
  examScheduleTable,
  examTable,
  usersTable,
  staffTable,
} from "../../../../db/schemas/index.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertException,
} from "../../../../utils/pg-utils.js";
import { and, desc, eq, sql, SQL } from "drizzle-orm";
import { getSelectClassSectionSqlQuery } from "../../../../utils/drizzle-raw-sql-queries.utils.js";
import { alias } from "drizzle-orm/pg-core";

@Injectable()
export class ExamSchedulesService {
  private readonly logger = new Logger(ExamSchedulesService.name);
  public constructor(@InjectDrizzle() private readonly db: Database) {}

  public async create(newExamScheduleData: NewExamSchedule) {
    try {
      return await executeQueryTakeFirstOrThrow(
        this.db
          .insert(examScheduleTable)
          .values(newExamScheduleData)
          .returning(),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to create exam schedule", error);
      handleDatabaseInsertException(error, {
        resource: "examSchedule",
      });
    }
  }

  public async findById(
    id: ExamSchedule["id"],
  ): Promise<ExamScheduleResponse | undefined> {
    try {
      const res = await this.findAll({ id });
      return res.items[0];
    } catch (err) {
      this.logger.error("Failed to find exam schedule by id", err);
      throw err;
    }
  }

  public async findAll(
    criteria?: Partial<Pick<ExamSchedule, "id" | "classSectionExamId">>,
  ) {
    const filters: SQL[] = [];

    if (criteria?.id) {
      filters.push(eq(examScheduleTable.id, criteria.id));
    }

    if (criteria?.classSectionExamId) {
      filters.push(
        eq(examScheduleTable.classSectionExamId, criteria.classSectionExamId),
      );
    }

    const subjectTeacherUserAlias = alias(usersTable, "subject_teacher_user");
    const subjectTeacherStaffAlias = alias(staffTable, "subject_teacher_staff");

    const {
      query: selectClassSectionQuery,
      classTeacherStaffAlias,
      classTeacherUserAlias,
    } = getSelectClassSectionSqlQuery();

    try {
      const [items, total] = await Promise.all([
        this.db
          .select({
            id: examScheduleTable.id,
            date: examScheduleTable.date,
            startTime: examScheduleTable.startTime,
            endTime: examScheduleTable.endTime,
            exam: sql<ExamScheduleResponse["exam"]>`json_build_object(
              'id', ${examTable.id},
              'name', ${examTable.name},
              'startDate', ${examTable.startDate},
              'endDate', ${examTable.endDate}
            )`,
            classSection: selectClassSectionQuery,
            subject: sql<ExamScheduleResponse["subject"]>`json_build_object(
              'id', ${subjectTable.id},
              'name', ${subjectTable.name},
              'teacher', json_build_object(
                'id', ${subjectTeacherUserAlias.id},
                'name', ${subjectTeacherUserAlias.name}
              )
            )`,
            totalMarks: examScheduleTable.totalMarks,
            passingMarks: examScheduleTable.passingMarks,
          })
          .from(examScheduleTable)
          .innerJoin(
            classSectionExamTable,
            eq(classSectionExamTable.id, examScheduleTable.classSectionExamId),
          )
          .innerJoin(examTable, eq(examTable.id, classSectionExamTable.examId))
          .innerJoin(
            classSectionTable,
            eq(classSectionTable.id, classSectionExamTable.classSectionId),
          )
          .innerJoin(classTable, eq(classTable.id, classSectionTable.classId))
          .innerJoin(
            sectionSubjectTable,
            eq(sectionSubjectTable.id, examScheduleTable.sectionSubjectId),
          )
          .innerJoin(
            subjectTable,
            eq(subjectTable.id, sectionSubjectTable.subjectId),
          )
          .innerJoin(
            subjectTeacherStaffAlias,
            eq(
              subjectTeacherStaffAlias.id,
              sectionSubjectTable.subjectTeacherId,
            ),
          )
          .innerJoin(
            subjectTeacherUserAlias,
            eq(subjectTeacherUserAlias.id, subjectTeacherStaffAlias.userId),
          )
          .innerJoin(
            classTeacherStaffAlias,
            eq(classTeacherStaffAlias.id, classSectionTable.classTeacherId),
          )
          .innerJoin(
            classTeacherUserAlias,
            eq(classTeacherUserAlias.id, classTeacherStaffAlias.userId),
          )
          .where(and(...filters))
          .orderBy(desc(examScheduleTable.date)),

        this.db.$count(examScheduleTable, and(...filters)),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all exam schedules", error);
      throw error;
    }
  }
}
