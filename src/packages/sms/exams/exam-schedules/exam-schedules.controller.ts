import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { Roles } from "../../../../core/roles/decorators/roles.decorator.js";
import { CreateExamScheduleDto } from "./dto/create-exam-schedule.dto.js";
import { ExamSchedulesService } from "./exam-schedules.service.js";
import { User } from "../../../../shared/decorators/user.decorator.js";
import type { AuthenticatedUser } from "../../../../core/auth/type/auth.type.js";

@Controller("sms/class-section-exams")
@ApiTags("Exam Schedules")
export class ExamSchedulesController {
  public constructor(
    private readonly examSchedulesService: ExamSchedulesService,
  ) {}

  @Roles(["BRANCH_ADMIN"])
  @Post(":classSectionExamId/schedules")
  public create(
    @Body() createExamScheduleDto: CreateExamScheduleDto,
    @Param("classSectionExamId", ParseUUIDPipe) classSectionExamId: string,
    @User() user: AuthenticatedUser,
  ) {
    return this.examSchedulesService.create({
      ...createExamScheduleDto,
      createdBy: user.id,
      classSectionExamId,
    });
  }

  @Roles(["BRANCH_ADMIN"])
  @Get(":classSectionExamId/schedules")
  public findAll(
    @Param("classSectionExamId", ParseUUIDPipe) classSectionExamId: string,
  ) {
    return this.examSchedulesService.findAll({ classSectionExamId });
  }
}
