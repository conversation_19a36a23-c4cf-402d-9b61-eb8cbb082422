import { ApiOperation, ApiResponse } from "@nestjs/swagger";
import {
  useCommonCreateResourceApiResponses,
  useCommonListResourceDocs,
} from "../../../../docs/shared/api-responses.js";
import { ExamDto } from "../dto/exam.dto.js";
import { ListExamsDto } from "../dto/list-exam-response.dto.js";

export function ApiDocCreateExam() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Create Exam",
      description: "Creates a new exam for a specific academic session",
    }),
    ApiResponse({
      status: 201,
      description: "Returns the newly created exam with complete details",
      type: ExamDto,
    }),
  );
}

export function ApiDocUpdateExam() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Update Exam",
      description: "Updates an existing exam",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the updated exam with complete details",
      type: ExamDto,
    }),
  );
}

export function ApiDocListExams() {
  return useCommonListResourceDocs(
    ApiOperation({
      summary: "List Exams",
      description: "Lists all exams for a specific academic session",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the list of exams",
      type: ListExamsDto,
    }),
  );
}
