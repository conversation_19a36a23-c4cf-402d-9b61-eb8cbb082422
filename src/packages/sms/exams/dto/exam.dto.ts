import { examBaseSchema } from "./create-exam.dto.js";
import { z } from "zod";
import { getUuidSchema } from "../../../../shared/schema/zod-common.schema.js";
import { createZodDto } from "nestjs-zod";
import { createSingleResourceResponseSchema } from "../../../../shared/schema/api-response.schema.js";

export const examResponseSchema = examBaseSchema.extend({
  id: getUuidSchema("Exam ID"),
  createdAt: z.date(),
});

export class ExamDto extends createZodDto(
  createSingleResourceResponseSchema(examResponseSchema),
) {}
