import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  Patch,
  HttpStatus,
  Query,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { ZodSerializerDto } from "nestjs-zod";
import { Roles } from "../../../../../core/roles/decorators/roles.decorator.js";
import { CreateExamScheduleDto } from "./dto/create-exam-schedule.dto.js";
import { UpdateExamScheduleDto } from "./dto/update-exam-schedule.dto.js";
import { ExamSchedulesService } from "./exam-schedules.service.js";
import { User } from "../../../../../shared/decorators/user.decorator.js";
import type { AuthenticatedUser } from "../../../../../core/auth/type/auth.type.js";
import {
  ApiDocCreateExamSchedule,
  ApiDocUpdateExamSchedule,
  ApiDocListExamSchedules,
} from "./docs/exam-schedules.docs.js";
import {
  ExamScheduleResponseDto,
  ListExamSchedulesResponseDto,
} from "./dto/exam-schedule-response.dto.js";
import { ListExamSchedulesQueryDto } from "./dto/list-exams-shedules-query.dto.js";

@Controller()
@ApiTags("Exam Schedules")
export class ExamSchedulesController {
  public constructor(
    private readonly examSchedulesService: ExamSchedulesService,
  ) {}

  @Roles(["BRANCH_ADMIN"])
  @Post("sms/class-section-exams/:classSectionExamId/schedules")
  @ApiDocCreateExamSchedule()
  @ZodSerializerDto(ExamScheduleResponseDto)
  public async create(
    @Body() createExamScheduleDto: CreateExamScheduleDto,
    @Param("classSectionExamId", ParseUUIDPipe) classSectionExamId: string,
    @User() user: AuthenticatedUser,
  ) {
    return await this.examSchedulesService.create({
      ...createExamScheduleDto,
      createdBy: user.id,
      classSectionExamId,
    });
  }

  @Roles(["BRANCH_ADMIN"])
  @Get("sms/class-section-exams/:classSectionExamId/schedules")
  @ZodSerializerDto(ListExamSchedulesResponseDto)
  @ApiDocListExamSchedules()
  public findAll(
    @Param("classSectionExamId", ParseUUIDPipe) classSectionExamId: string,
  ) {
    return this.examSchedulesService.findAll({ classSectionExamId });
  }

  @Roles(["BRANCH_ADMIN"])
  @Patch("sms/class-section-exams/:classSectionExamId/schedules/:scheduleId")
  @ApiDocUpdateExamSchedule()
  @ZodSerializerDto(ExamScheduleResponseDto)
  public update(
    @Body() updateExamScheduleDto: UpdateExamScheduleDto,
    @Param("scheduleId", ParseUUIDPipe) scheduleId: string,
    @User() user: AuthenticatedUser,
  ) {
    return this.examSchedulesService.update(
      scheduleId,
      updateExamScheduleDto,
      user.id,
    );
  }

  @Roles(["BRANCH_ADMIN", "TEACHER"])
  @Get("sms/class-sections/:classSectionId/exam-schedules")
  @ZodSerializerDto(ListExamSchedulesResponseDto)
  @ApiDocListExamSchedules()
  public async findById(
    @Param("classSectionId", ParseUUIDPipe) classSectionId: string,
    @Query() query: ListExamSchedulesQueryDto,
  ) {
    return this.examSchedulesService.findAll({ classSectionId }, query);
  }
}
