import { ApiOperation, ApiResponse } from "@nestjs/swagger";
import {
  useCommonCreateResourceApiResponses,
  useCommonListResourceDocs,
} from "../../../../../../docs/shared/api-responses.js";
import {
  ExamScheduleResponseDto,
  ListExamSchedulesResponseDto,
} from "../dto/exam-schedule-response.dto.js";
import { ClassSectionResultsResponseDto } from "../../exam-results/dto/exam-result-response.dto.js";

export function ApiDocCreateExamSchedule() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Create Exam Schedule",
      description:
        "Creates a new exam schedule for a specific class section exam and subject",
    }),
    ApiResponse({
      status: 201,
      description:
        "Returns the newly created exam schedule with complete details",
      type: ExamScheduleResponseDto,
    }),
  );
}

export function ApiDocUpdateExamSchedule() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Update Exam Schedule",
      description:
        "Updates an existing exam schedule with new date, time, or marks information",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the updated exam schedule with complete details",
      type: ExamScheduleResponseDto,
    }),
  );
}

export function ApiDocListExamSchedules() {
  return useCommonListResourceDocs(
    ApiOperation({
      summary: "List Exam Schedules",
      description:
        "Lists all exam schedules for a specific class section exam with exam, class section, and subject details",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the list of exam schedules",
      type: ListExamSchedulesResponseDto,
    }),
  );
}

export function ApiDocGetClassSectionResults() {
  return useCommonListResourceDocs(
    ApiOperation({
      summary: "Get Class Section Results",
      description: "Gets the results for a specific class section for an exam",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the results for the class section",
      type: ClassSectionResultsResponseDto,
    }),
  );
}
