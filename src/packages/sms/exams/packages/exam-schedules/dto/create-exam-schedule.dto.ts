import { z } from "zod";
import {
  getUuidSchema,
  pgDateSchema,
} from "../../../../../../shared/schema/zod-common.schema.js";
import { createZodDto } from "nestjs-zod";
import { pgTimestampSchema } from "../../../../../../shared/schema/zod-common.schema.js";

export const examScheduleBaseSchema = z.object({
  date: pgDateSchema,
  startTime: pgTimestampSchema,
  endTime: pgTimestampSchema,
  sectionSubjectId: getUuidSchema("Section Subject ID"),
  totalMarks: z.number().positive().max(1000, {
    message: "Total marks cannot exceed 1000",
  }),
  passingMarks: z.number().positive().max(1000, {
    message: "Passing marks cannot exceed 1000",
  }),
});

export const createExamScheduleSchema = examScheduleBaseSchema.refine(
  data => {
    return data.startTime < data.endTime;
  },
  {
    message: "Start time must be before end time",
  },
);

export class CreateExamScheduleDto extends createZodDto(
  createExamScheduleSchema,
) {}
