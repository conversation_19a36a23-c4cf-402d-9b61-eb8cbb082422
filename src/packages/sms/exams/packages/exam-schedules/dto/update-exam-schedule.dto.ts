import { createZodDto } from "nestjs-zod";
import { examScheduleBaseSchema } from "./create-exam-schedule.dto.js";

export const updateExamScheduleSchema = examScheduleBaseSchema.partial().refine(
  data => {
    // Only validate time constraint if both times are provided
    if (data.startTime && data.endTime) {
      return data.startTime < data.endTime;
    }
    return true;
  },
  {
    message: "Start time must be before end time",
  },
);

export class UpdateExamScheduleDto extends createZodDto(updateExamScheduleSchema) {}
