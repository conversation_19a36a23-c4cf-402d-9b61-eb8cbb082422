import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import { getUuidSchema } from "../../../../../../shared/schema/zod-common.schema.js";
import {
  createSingleResourceResponseSchema,
  createPaginatedResourceResponseSchema,
} from "../../../../../../shared/schema/api-response.schema.js";
import { classSectionResponseSchema } from "../../../../sections/dto/class-sections.dto.js";
import { examResponseSchema } from "../../../dto/exam.dto.js";
import { examScheduleBaseSchema } from "./create-exam-schedule.dto.js";

export const examScheduleResponseSchema = examScheduleBaseSchema
  .omit({ sectionSubjectId: true, startTime: true, endTime: true })
  .extend({
    id: getUuidSchema("Exam Schedule ID"),
    startTime: z.coerce.date().describe("Start time of the exam"),
    endTime: z.coerce.date().describe("End time of the exam"),
    exam: examResponseSchema,
    classSection: classSectionResponseSchema,
    subject: z
      .object({
        id: getUuidSchema("Subject ID"),
        name: z.string().describe("Name of the subject"),
        teacher: z
          .object({
            id: getUuidSchema("Subject Teacher ID"),
            name: z.string().describe("Name of the subject teacher"),
          })
          .describe("Subject teacher information"),
      })
      .describe("Subject information"),
    totalMarks: z.number().describe("Total marks for the exam"),
    passingMarks: z.number().describe("Passing marks for the exam"),
  });

export class ExamScheduleResponseDto extends createZodDto(
  createSingleResourceResponseSchema(examScheduleResponseSchema),
) {}

export const listExamSchedulesResponseSchema = z.object({
  items: z.array(examScheduleResponseSchema),
  total: z.number().describe("Total number of exam schedules"),
});

export class ListExamSchedulesResponseDto extends createZodDto(
  createPaginatedResourceResponseSchema(examScheduleResponseSchema),
) {}
