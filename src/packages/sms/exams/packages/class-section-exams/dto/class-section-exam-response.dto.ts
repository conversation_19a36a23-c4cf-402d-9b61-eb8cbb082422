import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import { getUuidSchema } from "../../../../../../shared/schema/zod-common.schema.js";
import {
  createSingleResourceResponseSchema,
  createPaginatedResourceResponseSchema,
} from "../../../../../../shared/schema/api-response.schema.js";
import { classSectionResponseSchema } from "../../../../sections/dto/class-sections.dto.js";
import { examResponseSchema } from "../../../dto/exam.dto.js";

export const classSectionExamResponseSchema = z.object({
  id: getUuidSchema("Class Section Exam ID"),
  exam: examResponseSchema,
  classSection: classSectionResponseSchema,
  createdAt: z
    .date()
    .describe("Date when the exam was assigned to the class section"),
});

export class ClassSectionExamResponseDto extends createZodDto(
  createSingleResourceResponseSchema(classSectionExamResponseSchema),
) {}

export const listClassSectionExamsResponseSchema = z.object({
  items: z.array(classSectionExamResponseSchema),
  total: z.number().describe("Total number of class section exam assignments"),
});

export class ListClassSectionExamsResponseDto extends createZodDto(
  createPaginatedResourceResponseSchema(classSectionExamResponseSchema),
) {}
