import { z } from "zod";
import { getUuidSchema } from "../../../../../../shared/schema/zod-common.schema.js";
import { createZodDto } from "nestjs-zod";

export const assignClassSectionExamSchema = z.object({
  classSectionIds: z.array(getUuidSchema("Class Section ID")),
});

export class AssignClassSectionExamDto extends createZodDto(
  assignClassSectionExamSchema,
) {}

// Update schema for class section exam assignments (for removing assignments)
export const updateClassSectionExamSchema = z.object({
  classSectionIds: z.array(getUuidSchema("Class Section ID")),
});

export class UpdateClassSectionExamDto extends createZodDto(
  updateClassSectionExamSchema,
) {}
