import { Injectable, Logger } from "@nestjs/common";
import { InjectDrizzle } from "../../../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../../../db/type.js";
import {
  classSectionExamTable,
  classSectionTable,
  classTable,
  examTable,
} from "../../../../../db/schemas/index.js";
import { handleDatabaseInsertException } from "../../../../../utils/pg-utils.js";
import {
  ClassSectionExam,
  ClassSectionExamResponse,
} from "./types/class-section-exam.type.js";
import { and, eq, sql, SQL } from "drizzle-orm";
import {
  PaginatedApiResponse,
  ServiceOptions,
} from "../../../../../shared/types/shared.types.js";
import { getSelectClassSectionSqlQuery } from "../../../../../utils/drizzle-raw-sql-queries.utils.js";

@Injectable()
export class ClassSectionExamsService {
  private readonly logger = new Logger(ClassSectionExamsService.name);

  public constructor(@InjectDrizzle() private readonly db: Database) {}

  public async assignExamToClassSections(
    examId: string,
    classSectionIds: string[],
  ) {
    try {
      return await this.db
        .insert(classSectionExamTable)
        .values(
          classSectionIds.map(id => ({
            classSectionId: id,
            examId,
          })),
        )
        .returning();
    } catch (error: unknown) {
      this.logger.error("Failed to assign exam to class sections", error);
      handleDatabaseInsertException(error, {
        resource: "classSectionExam",
        messages: {
          uniqueConstraintError: "Exam already assigned to this class section",
        },
      });
    }
  }

  public async findAll(
    criteria: Partial<Pick<ClassSectionExam, "examId">>,
  ): Promise<PaginatedApiResponse<ClassSectionExamResponse>> {
    const filters: SQL[] = [];
    if (criteria.examId) {
      filters.push(eq(classSectionExamTable.examId, criteria.examId));
    }

    try {
      const [items, total] = await Promise.all([
        this.buildSelectQuery(filters).execute(),

        this.db.$count(classSectionExamTable, and(...filters)),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all assignments", error);
      throw error;
    }
  }

  /**
   * Updates the class sections assigned to an exam by replacing existing assignments
   * @param examId - The exam ID
   * @param classSectionIds - Array of class section IDs to assign to the exam
   */
  public async updateExamClassSectionAssignments(
    examId: string,
    classSectionIds: string[],
  ) {
    try {
      // Use a transaction to ensure atomicity
      return await this.db.transaction(async tx => {
        // First, delete existing assignments for this exam
        await tx
          .delete(classSectionExamTable)
          .where(eq(classSectionExamTable.examId, examId));

        // Then, insert new assignments
        if (classSectionIds.length > 0) {
          const newAssignments = await tx
            .insert(classSectionExamTable)
            .values(
              classSectionIds.map(id => ({
                classSectionId: id,
                examId,
              })),
            )
            .returning();

          return newAssignments;
        }

        return [];
      });
    } catch (error: unknown) {
      this.logger.error(
        "Failed to update exam class section assignments",
        error,
      );
      handleDatabaseInsertException(error, {
        resource: "classSectionExam",
      });
    }
  }

  private buildSelectQuery(filters: SQL[], options?: ServiceOptions) {
    const {
      query: selectClassSectionQuery,
      classTeacherStaffAlias,
      classTeacherUserAlias,
    } = getSelectClassSectionSqlQuery();
    const dbClient = options?.pgTrx ?? this.db;
    return dbClient
      .select({
        id: classSectionExamTable.id,
        exam: sql<ClassSectionExamResponse["exam"]>`json_build_object(
              'id', ${examTable.id},
              'name', ${examTable.name},
              'startDate', ${examTable.startDate},
              'endDate', ${examTable.endDate},
              'createdAt', ${examTable.createdAt}
        )`,
        classSection: selectClassSectionQuery,
        createdAt: classSectionExamTable.createdAt,
      })
      .from(classSectionExamTable)
      .innerJoin(examTable, eq(examTable.id, classSectionExamTable.examId))
      .innerJoin(
        classSectionTable,
        eq(classSectionTable.id, classSectionExamTable.classSectionId),
      )
      .innerJoin(classTable, eq(classTable.id, classSectionTable.classId))
      .innerJoin(
        classTeacherStaffAlias,
        eq(classTeacherStaffAlias.id, classSectionTable.classTeacherId),
      )
      .innerJoin(
        classTeacherUserAlias,
        eq(classTeacherUserAlias.id, classTeacherStaffAlias.userId),
      )
      .where(and(...filters));
  }
}
