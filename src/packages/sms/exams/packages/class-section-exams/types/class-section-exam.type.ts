import { classSectionExamTable } from "../../../../../../db/schemas/index.js";
import { z } from "zod";
import { classSectionExamResponseSchema } from "../dto/class-section-exam-response.dto.js";
export type ClassSectionExam = typeof classSectionExamTable.$inferSelect;
export type NewClassSectionExam = typeof classSectionExamTable.$inferInsert;

export type ClassSectionExamResponse = z.infer<
  typeof classSectionExamResponseSchema
>;
