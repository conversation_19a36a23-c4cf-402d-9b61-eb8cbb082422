import { ApiOperation, ApiResponse } from "@nestjs/swagger";
import {
  useCommonCreateResourceApiResponses,
  useCommonListResourceDocs,
} from "../../../../../../docs/shared/api-responses.js";
import {
  ClassSectionExamResponseDto,
  ListClassSectionExamsResponseDto,
} from "../dto/class-section-exam-response.dto.js";

export function ApiDocAssignExamToClassSections() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Assign Exam to Class Sections",
      description:
        "Assigns an exam to multiple class sections, creating exam schedules for each section",
    }),
    ApiResponse({
      status: 201,
      description: "Returns the newly created class section exam assignments",
      type: ClassSectionExamResponseDto,
      isArray: true,
    }),
  );
}

export function ApiDocUpdateClassSectionExamAssignments() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Update Class Section Exam Assignments",
      description:
        "Updates the class sections assigned to an exam by replacing existing assignments",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the updated class section exam assignments",
      type: ClassSectionExamResponseDto,
      isArray: true,
    }),
  );
}

export function ApiDocListClassSectionExams() {
  return useCommonListResourceDocs(
    ApiOperation({
      summary: "List Class Section Exams",
      description:
        "Lists all class sections assigned to a specific exam with their details",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the list of class section exam assignments",
      type: ListClassSectionExamsResponseDto,
    }),
  );
}
