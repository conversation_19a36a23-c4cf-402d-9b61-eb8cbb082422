import {
  Body,
  Controller,
  Get,
  Post,
  Patch,
  Param,
  ParseUUIDPipe,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { ZodSerializerDto } from "nestjs-zod";
import { ExamResultsService } from "./exam-results.service.js";
import { User } from "../../../../../shared/decorators/user.decorator.js";
import type { AuthenticatedUser } from "../../../../../core/auth/type/auth.type.js";
import { Roles } from "../../../../../core/roles/decorators/roles.decorator.js";
import { CreateExamResultDto } from "./dto/create-exam-result.dto.js";
import { UpdateExamResultDto } from "./dto/update-exam-result.dto.js";
import {
  ApiDocCreateExamResult,
  ApiDocUpdateExamResult,
  ApiDocListExamResults,
  ApiDocGetClassSectionResults,
} from "./docs/exam-results.docs.js";
import {
  ClassSectionResultsResponseDto,
  ExamResultResponseDto,
  ListExamResultsResponseDto,
} from "./dto/exam-result-response.dto.js";

@Controller()
@ApiTags("Exam Results")
export class ExamResultsController {
  public constructor(private readonly examResultsService: ExamResultsService) {}

  @Roles(["BRANCH_ADMIN", "TEACHER"])
  @Post("sms/exam-schedules/:examScheduleId/results")
  @ApiDocCreateExamResult()
  @ZodSerializerDto(ExamResultResponseDto)
  public create(
    @Body() createExamResultDto: CreateExamResultDto,
    @Param("examScheduleId", ParseUUIDPipe) examScheduleId: string,
    @User() user: AuthenticatedUser,
  ) {
    return this.examResultsService.create({
      ...createExamResultDto,
      createdBy: user.id,
      examScheduleId,
    });
  }

  @Roles(["BRANCH_ADMIN", "TEACHER"])
  @Get("sms/exam-schedules/:examScheduleId/results/:resultId")
  @ZodSerializerDto(ExamResultResponseDto)
  public async findFirstOrThrow(
    @Param("resultId", ParseUUIDPipe) resultId: string,
  ) {
    return this.examResultsService.findFirstOrThrow({ id: resultId });
  }

  @Roles(["BRANCH_ADMIN", "TEACHER"])
  @Post("sms/exam-schedules/:examScheduleId/results/bulk")
  @ZodSerializerDto(ExamResultResponseDto)
  public async createInBulk(
    @Body() createExamResultDto: CreateExamResultDto[],
    @Param("examScheduleId", ParseUUIDPipe) examScheduleId: string,
    @User() user: AuthenticatedUser,
  ) {
    // return this.examResultsService.findFirstOrThrow({ id: resultId });
  }

  @Roles(["BRANCH_ADMIN", "TEACHER"])
  @Get("sms/exam-schedules/:examScheduleId/results")
  @ZodSerializerDto(ListExamResultsResponseDto)
  @ApiDocListExamResults()
  public findAll(
    @Param("examScheduleId", ParseUUIDPipe) examScheduleId: string,
  ) {
    return this.examResultsService.findAll({ examScheduleId });
  }

  @Roles(["BRANCH_ADMIN", "TEACHER"])
  @Patch("sms/exam-schedules/:examScheduleId/results/:resultId")
  @ZodSerializerDto(ExamResultResponseDto)
  @ApiDocUpdateExamResult()
  public update(
    @Body() updateExamResultDto: UpdateExamResultDto,
    @Param("resultId", ParseUUIDPipe) resultId: string,
    @User() user: AuthenticatedUser,
  ) {
    return this.examResultsService.update(
      resultId,
      updateExamResultDto,
      user.id,
    );
  }

  @Roles(["BRANCH_ADMIN", "TEACHER", "INSTITUTE_OWNER"])
  @Get("sms/class-sections/:classSectionId/exam-results")
  @ApiDocGetClassSectionResults()
  @ZodSerializerDto(ClassSectionResultsResponseDto)
  public async findById(
    @Param("classSectionId", ParseUUIDPipe) sectionId: string,
  ) {
    return this.examResultsService.findClassSectionResults(sectionId);
  }
}
