import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import { getUuidSchema } from "../../../../../../shared/schema/zod-common.schema.js";

export const listExamResultsQuerySchema = z.object({
  enrollmentId: getUuidSchema("Enrollment ID").optional(),
  isAbsent: z.boolean().optional(),
  marksObtained: z.number().optional(),
  createdBy: getUuidSchema("Created By").optional(),
});

export class ListExamResultsQueryDto extends createZodDto(
  listExamResultsQuerySchema,
) {}
