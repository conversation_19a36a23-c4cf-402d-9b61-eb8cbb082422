import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import { getUuidSchema } from "../../../../../../shared/schema/zod-common.schema.js";

export const createExamResultSchema = z.object({
  enrollmentId: getUuidSchema("Enrollment ID"),
  marksObtained: z
    .number()
    .positive()
    .max(1000, {
      message: "Marks obtained cannot exceed 1000",
    })
    .nullable(),
  remarks: z.string().nullable().optional(),
  isAbsent: z.boolean().default(false),
});

export class CreateExamResultDto extends createZodDto(createExamResultSchema) {}
