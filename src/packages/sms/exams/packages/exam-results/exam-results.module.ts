import { Modu<PERSON> } from "@nestjs/common";
import { ExamResultsService } from "./exam-results.service.js";
import { ExamResultsController } from "./exam-results.controller.js";
import { EnrollmentsModule } from "../../../students/enrollments/enrollments.module.js";
import { ExamSchedulesModule } from "../exam-schedules/exam-schedules.module.js";
import { StaffModule } from "../../../staff/staff.module.js";

@Module({
  imports: [EnrollmentsModule, ExamSchedulesModule, StaffModule],
  providers: [ExamResultsService],
  controllers: [ExamResultsController],
})
export class ExamResultsModule {}
