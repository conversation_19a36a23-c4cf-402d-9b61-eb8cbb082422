import { ApiOperation, ApiResponse } from "@nestjs/swagger";
import {
  useCommonCreateResourceApiResponses,
  useCommonListResourceDocs,
} from "../../../../../../docs/shared/api-responses.js";
import {
  ExamResultResponseDto,
  ListExamResultsResponseDto,
} from "../dto/exam-result-response.dto.js";

export function ApiDocCreateExamResult() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Create Exam Result",
      description:
        "Creates a new exam result for a student in a specific exam schedule",
    }),
    ApiResponse({
      status: 201,
      description:
        "Returns the newly created exam result with complete details",
      type: ExamResultResponseDto,
    }),
  );
}

export function ApiDocUpdateExamResult() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Update Exam Result",
      description:
        "Updates an existing exam result with new marks, remarks, or attendance status",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the updated exam result with complete details",
      type: ExamResultResponseDto,
    }),
  );
}

export function ApiDocListExamResults() {
  return useCommonListResourceDocs(
    ApiOperation({
      summary: "List Exam Results",
      description:
        "Lists all exam results for a specific exam schedule with student and class section details",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the list of exam results",
      type: ListExamResultsResponseDto,
    }),
  );
}
