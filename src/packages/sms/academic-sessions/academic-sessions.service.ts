import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { Updateable } from "kysely";
import {
  NewAcademicSession,
  AcademicSession,
} from "./types/academic-session.types.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertException,
} from "../../../utils/pg-utils.js";
import type { Database } from "../../../db/type.js";
import { ServiceOptions } from "../../../shared/types/shared.types.js";
import { academicSessionTable } from "../../../db/schemas/index.js";
import { and, eq, SQL } from "drizzle-orm";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";

@Injectable()
export class AcademicSessionsService {
  private readonly logger = new Logger(AcademicSessionsService.name);

  public constructor(@InjectDrizzle() private readonly db: Database) {}

  public async create(data: NewAcademicSession): Promise<AcademicSession> {
    try {
      return await this.db.transaction(async trx => {
        // deactivate previous session
        await this.deactivateBranchPreviousSessions(data.branchId, {
          pgTrx: trx,
        });
        return await executeQueryTakeFirstOrThrow(
          this.db.insert(academicSessionTable).values(data).returning(),
        );
      });
    } catch (error: unknown) {
      handleDatabaseInsertException(error, {
        resource: "academicSession",
        logger: this.logger,
      });
    }
  }

  public async findById(
    id: AcademicSession["id"],
  ): Promise<AcademicSession | undefined> {
    try {
      return await this.db.query.academicSessionTable.findFirst({
        where: eq(academicSessionTable.id, id),
      });
    } catch (error: unknown) {
      this.logger.error("Failed to find academic session by id", error);
      throw error;
    }
  }

  public async update(
    criteria: Partial<Pick<AcademicSession, "branchId" | "id" | "isActive">>,
    data: Updateable<AcademicSession>,
    options?: ServiceOptions,
  ) {
    const dbClient = options?.pgTrx ?? this.db;
    const filters: SQL[] = [];

    if (criteria.id) {
      filters.push(eq(academicSessionTable.id, criteria.id));
    }

    if (criteria.branchId) {
      filters.push(eq(academicSessionTable.branchId, criteria.branchId));
    }

    if (criteria.isActive) {
      filters.push(eq(academicSessionTable.isActive, criteria.isActive));
    }

    try {
      return await executeQueryTakeFirstOrThrow(
        dbClient
          .update(academicSessionTable)
          .set(data)
          .where(and(...filters))
          .returning(),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to update academic session", error);
      throw error;
    }
  }

  public async findAll(
    criteria: Partial<AcademicSession>,
    options?: ServiceOptions,
  ): Promise<AcademicSession[]> {
    const filters: SQL[] = [];

    if (criteria.id) {
      filters.push(eq(academicSessionTable.id, criteria.id));
    }

    if (criteria.branchId) {
      filters.push(eq(academicSessionTable.branchId, criteria.branchId));
    }

    if (criteria.isActive) {
      filters.push(eq(academicSessionTable.isActive, criteria.isActive));
    }

    if (criteria.createdAt) {
      filters.push(eq(academicSessionTable.createdAt, criteria.createdAt));
    }

    if (criteria.startDate) {
      filters.push(eq(academicSessionTable.startDate, criteria.startDate));
    }

    if (criteria.endDate) {
      filters.push(eq(academicSessionTable.endDate, criteria.endDate));
    }

    const dbClient = options?.pgTrx ?? this.db;
    try {
      return await dbClient.query.academicSessionTable.findMany({
        where: and(...filters),
      });
    } catch (error: unknown) {
      this.logger.error("Failed to find all academic sessions", error);
      throw error;
    }
  }

  public async findOne(
    criteria: Partial<AcademicSession>,
    options?: ServiceOptions,
  ): Promise<AcademicSession | undefined> {
    try {
      return (await this.findAll(criteria, options))[0];
    } catch (error: unknown) {
      this.logger.error("Failed to find one academic session", error);
      throw error;
    }
  }

  private async deactivateBranchPreviousSessions(
    branchId: AcademicSession["branchId"],
    serviceOptions: ServiceOptions,
  ) {
    try {
      await this.update(
        { branchId, isActive: true },
        { isActive: false },
        serviceOptions,
      );
    } catch (error: unknown) {
      this.logger.error(`Failed to deactivate branch previous sessions`, error);
      handleDatabaseInsertException(error, {
        resource: "academicSession",
        logger: this.logger,
      });
    }
  }
}
