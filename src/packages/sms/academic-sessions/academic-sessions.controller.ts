import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUI<PERSON>ipe,
  Post,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { AcademicSessionsService } from "./academic-sessions.service.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { CreateAcademicSessionDto } from "./dto/academic-sessions.dto.js";
import { SessionBranchIdParamDto } from "./dto/academic-sessions-param.dto.js";

@Controller("sms")
@ApiTags("Academic Sessions")
export class AcademicSessionsController {
  constructor(
    private readonly academicSessionsService: AcademicSessionsService,
  ) {}

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Post("/branches/:branchId/sessions")
  public async create(
    @Param() param: SessionBranchIdParamDto,
    @Body() createAcademicSessionDto: CreateAcademicSessionDto,
  ) {
    return this.academicSessionsService.create({
      ...createAcademicSessionDto,
      ...param,
    });
  }

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Get("/branches/:branchId/sessions")
  public async get(@Param("branchId", ParseUUIDPipe) branchId: string) {
    return this.academicSessionsService.findAll({ branchId });
  }
}
