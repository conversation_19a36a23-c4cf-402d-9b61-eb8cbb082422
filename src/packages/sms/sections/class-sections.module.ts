import { Modu<PERSON> } from "@nestjs/common";
import { ClassSectionsController } from "./class-sections.controller.js";
import { ClassSectionsService } from "./class-sections.service.js";
import { StudentCardTemplateModule } from "./packages/student-card-template/student-card-template.module.js";

@Module({
  controllers: [ClassSectionsController],
  providers: [ClassSectionsService],
  exports: [ClassSectionsService],
  imports: [StudentCardTemplateModule],
})
export class ClassSectionsModule {}
