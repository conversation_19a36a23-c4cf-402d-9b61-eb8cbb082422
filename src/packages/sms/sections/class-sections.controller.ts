import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
  Res,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { ClassSectionsService } from "./class-sections.service.js";
import {
  ClassIdParamDto,
  ClassSectionParamsDto,
} from "./dto/class-sections-params.dto.js";
import { CreateClassSectionDto } from "./dto/class-sections.dto.js";
import type { Response } from "express";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { ListAllEntitiesQueryDto } from "../../../shared/dto/query.dto.js";
import { ApiDocCreateClassSection } from "./docs/class-sections.docs.js";

@Controller("sms/classes")
@ApiTags("Class Sections")
export class ClassSectionsController {
  public constructor(private readonly sectionsService: ClassSectionsService) {}

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Post("/:classId/sections")
  @ApiDocCreateClassSection()
  public async create(
    @Body() createClassSectionDto: CreateClassSectionDto,
    @Param() param: ClassIdParamDto,
    @Res() res: Response,
  ) {
    const { id } = await this.sectionsService.create({
      ...createClassSectionDto,
      classId: param.classId,
    });
    res.setHeader("Location", id);
    res.status(HttpStatus.CREATED).send();
  }

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Get("/:classId/sections")
  public getAllClassSections(
    @Param("classId", ParseUUIDPipe) classId: string,
    @Query() query: ListAllEntitiesQueryDto,
  ) {
    return this.sectionsService.getAllByClassId(classId, query);
  }

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN", "TEACHER"])
  @Get("/:classId/sections/:sectionId")
  public async getClassSection(@Param() param: ClassSectionParamsDto) {
    return this.sectionsService.findByIdOrThrow(param.sectionId);
  }
}
