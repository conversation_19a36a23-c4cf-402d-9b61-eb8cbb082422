import { Injectable, Logger, NotFoundException } from "@nestjs/common";

import {
  ClassSection,
  ClassSectionResponse,
  NewClassSection,
} from "./types/class-sections.types.js";
import {
  EntityId,
  ListAllEntitiesQueryOptions,
  ServiceOptions,
} from "../../../shared/types/shared.types.js";
import {
  handleDatabaseInsertException,
  executeQueryTakeFirstOrThrow,
} from "../../../utils/pg-utils.js";
import { Class } from "../classes/types/classes.types.js";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../db/type.js";
import {
  classSectionTable,
  classTable,
  enrollmentTable,
  staffTable,
  usersTable,
} from "../../../db/schemas/index.js";
import { and, desc, eq, SQL, sql } from "drizzle-orm";

@Injectable()
export class ClassSectionsService {
  private readonly logger = new Logger(ClassSectionsService.name);

  public constructor(@InjectDrizzle() private readonly db: Database) {}

  public async create(
    createSectionPayload: NewClassSection,
    options?: ServiceOptions,
  ): Promise<EntityId> {
    try {
      const client = options?.pgTrx ?? this.db;
      return await executeQueryTakeFirstOrThrow(
        client
          .insert(classSectionTable)
          .values(createSectionPayload)
          .returning({ id: classSectionTable.id }),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to create section", error);
      handleDatabaseInsertException(error, {
        resource: "classSection",
        logger: this.logger,
        messages: {
          uniqueConstraintError: "Section with same name already exists",
        },
      });
    }
  }

  public async update(
    id: ClassSection["id"],
    updateSectionPayload: Partial<ClassSection>,
    options?: ServiceOptions,
  ): Promise<EntityId> {
    try {
      const client = options?.pgTrx ?? this.db;
      return await executeQueryTakeFirstOrThrow(
        client
          .update(classSectionTable)
          .set(updateSectionPayload)
          .where(eq(classSectionTable.id, id))
          .returning({ id: classSectionTable.id }),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to update section", error);
      throw error;
    }
  }

  public async createOrUpdate(
    payload: NewClassSection,
    options?: ServiceOptions,
  ): Promise<EntityId> {
    try {
      const sections = await this.find(
        {
          id: payload.id,
          classId: payload.classId,
        },
        options,
      );

      const existingSection = sections[0];

      if (existingSection) {
        return await this.update(existingSection.id, payload, options);
      }
      return await this.create(payload, options);
    } catch (error: unknown) {
      this.logger.error("Failed to create or update section", error);
      handleDatabaseInsertException(error, {
        resource: "classSection",
        logger: this.logger,
      });
    }
  }
  /*
  |--------------------------------------------------------------------------
  | Create Many Sections
  |--------------------------------------------------------------------------
  |
  | This method is used to create multiple class sections in a single operation.
  | It is designed to be called internally from other services and not directly
  | from controllers. The method accepts an array of section data and inserts
  | them into the database, returning the created sections with their IDs.
  |
  */
  public async createMany(
    createManySectionPayload: NewClassSection[],
    options?: ServiceOptions,
  ): Promise<void> {
    try {
      const client = options?.pgTrx ?? this.db;
      await client.insert(classSectionTable).values(createManySectionPayload);
    } catch (error: unknown) {
      this.logger.error("Failed to create sections", error);
      handleDatabaseInsertException(error, {
        resource: "classSection",
        logger: this.logger,
      });
    }
  }

  public async findById(id: ClassSection["id"], options?: ServiceOptions) {
    try {
      const res = await this.find({ id }, options);
      return res[0];
    } catch (error: unknown) {
      this.logger.error("Failed to find section by id", error);
      throw error;
    }
  }

  public async findByIdOrThrow(
    id: ClassSection["id"],
    options?: ServiceOptions,
  ) {
    let existingSection: ClassSectionResponse | undefined;
    try {
      existingSection = await this.findById(id, options);
    } catch (error: unknown) {
      this.logger.error("Failed to find section by id", error);
      throw error;
    }
    if (!existingSection) {
      throw new NotFoundException(`Section with id: ${id} not found`);
    }
    return existingSection;
  }

  public async getAllByClassId(
    classId: Class["id"],
    queryOptions: ListAllEntitiesQueryOptions,
  ) {
    try {
      const [items, total] = await Promise.all([
        this.find({ classId }, {}, queryOptions),
        this.db.$count(
          classSectionTable,
          eq(classSectionTable.classId, classId),
        ),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to get all sections", error);
      throw error;
    }
  }

  public async verifyClassSectionExists(
    classSectionId: string,
    options?: ServiceOptions,
  ) {
    let existingClassSection: ClassSectionResponse | undefined;
    try {
      existingClassSection = await this.findById(classSectionId, options);
    } catch (error: unknown) {
      this.logger.error("Failed to verify class section exists", error);
      throw error;
    }

    if (!existingClassSection) {
      throw new NotFoundException(
        `Class section with id: ${classSectionId} not found`,
      );
    }
  }

  public async findByAcademicSessionId(
    academicSessionId: string,
    options?: ServiceOptions,
  ) {
    try {
      return await this.find({ academicSessionId }, options);
    } catch (error: unknown) {
      this.logger.error(
        "Failed to find sections by academic session id",
        error,
      );
      throw error;
    }
  }

  public async find(
    criteria: Partial<ClassSection & Partial<Pick<Class, "academicSessionId">>>,
    options?: ServiceOptions,
    queryOptions?: ListAllEntitiesQueryOptions,
  ) {
    const filters: SQL[] = [];

    if (criteria.id) {
      filters.push(eq(classSectionTable.id, criteria.id));
    }

    if (criteria.classId) {
      filters.push(eq(classSectionTable.classId, criteria.classId));
    }

    if (criteria.academicSessionId) {
      filters.push(
        eq(classTable.academicSessionId, criteria.academicSessionId),
      );
    }

    if (criteria.classTeacherId) {
      filters.push(
        eq(classSectionTable.classTeacherId, criteria.classTeacherId),
      );
    }

    const client = options?.pgTrx ?? this.db;

    const query = client
      .select({
        id: classSectionTable.id,
        name: classSectionTable.name,
        class: sql<ClassSectionResponse["class"]>`json_build_object(
        'id', ${classTable.id},
        'name', ${classTable.name}
        )`,
        classTeacher: sql<
          ClassSectionResponse["classTeacher"]
        >`json_build_object(
        'id', ${usersTable.id},
        'name', ${usersTable.name}
        )`,
        totalStudents: sql<number>`(
        SELECT COUNT(*)::int
        FROM ${enrollmentTable}
        WHERE ${enrollmentTable.classSectionId} = ${classSectionTable.id}
        )`,
        createdAt: classSectionTable.createdAt,
        isActive: classSectionTable.isActive,
      })
      .from(classSectionTable)
      .innerJoin(classTable, eq(classTable.id, classSectionTable.classId))
      .innerJoin(
        staffTable,
        eq(staffTable.userId, classSectionTable.classTeacherId),
      )
      .innerJoin(usersTable, eq(usersTable.id, staffTable.userId))
      .orderBy(desc(classSectionTable.createdAt))
      .where(and(...filters));

    if (queryOptions?.limit) {
      query.limit(queryOptions.limit);
    }
    if (queryOptions?.offset) {
      query.offset(queryOptions.offset);
    }

    return query.execute();
  }
}
