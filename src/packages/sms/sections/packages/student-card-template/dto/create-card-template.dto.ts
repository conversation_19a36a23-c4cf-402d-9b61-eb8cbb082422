import { createZodDto } from "nestjs-zod";
import { z } from "zod";

export const createCardTemplateSchema = z.object({
  title: z
    .string()
    .min(1, "Title is required")
    .max(100, "Title must be less than 100 characters"),
  watermark: z
    .string()
    .min(1, "Watermark is required")
    .max(100, "Watermark must be less than 100 characters"),
  headerImage: z.string().url("Invalid header image URL"),
  backgroundImage: z.string().url("Invalid background image URL"),
});

export class CreateCardTemplateDto extends createZodDto(
  createCardTemplateSchema,
) {}
