import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUI<PERSON>ipe,
  Patch,
  Post,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { StudentCardTemplateService } from "./student-card-template.service.js";
import { CreateCardTemplateDto } from "./dto/create-card-template.dto.js";
import { Roles } from "../../../../../core/roles/decorators/roles.decorator.js";
import { UpdateCardTemplateDto } from "./dto/update-card-template.dto.js";

@Controller("sms/branches")
@ApiTags("Student Card Templates")
export class StudentCardTemplateController {
  public constructor(
    private readonly studentCardTemplateService: StudentCardTemplateService,
  ) {}

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Post(":branchId/student-card-templates")
  public create(
    @Body() createCardTemplateDto: CreateCardTemplateDto,
    @Param("branchId", ParseUUIDPipe) branchId: string,
  ) {
    return this.studentCardTemplateService.create({
      ...createCardTemplateDto,
      branchId,
    });
  }

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Get(":branchId/student-card-templates")
  public get(@Param("branchId", ParseUUIDPipe) branchId: string) {
    return this.studentCardTemplateService.find({
      branchId,
    });
  }

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Get(":branchId/student-card-templates/:templateId")
  public getById(
    @Param("branchId", ParseUUIDPipe) branchId: string,
    @Param("templateId", ParseUUIDPipe) templateId: string,
  ) {
    return this.studentCardTemplateService.findById(templateId);
  }

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Patch(":branchId/student-card-templates/:templateId")
  public update(
    @Body() updateCardTemplateDto: UpdateCardTemplateDto,
    @Param("branchId", ParseUUIDPipe) branchId: string,
    @Param("templateId", ParseUUIDPipe) templateId: string,
  ) {
    return this.studentCardTemplateService.update(
      templateId,
      updateCardTemplateDto,
    );
  }
}
