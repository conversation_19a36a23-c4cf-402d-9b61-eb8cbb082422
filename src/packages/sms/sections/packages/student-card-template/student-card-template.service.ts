import { ConflictException, Injectable, Logger } from "@nestjs/common";
import { InjectDrizzle } from "../../../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../../../db/type.js";
import {
  NewStudentCardTemplate,
  StudentCardTemplate,
  StudentCardTemplateUpdate,
} from "./types/student-card-template.type.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertException,
} from "../../../../../utils/pg-utils.js";
import { studentCardTemplateTable } from "../../../../../db/schemas/student-card-template.schema.js";
import { and, eq, SQL } from "drizzle-orm";

@Injectable()
export class StudentCardTemplateService {
  private readonly logger = new Logger(StudentCardTemplateService.name);

  public constructor(@InjectDrizzle() private readonly db: Database) {}

  public async create(
    createCardTemplateData: NewStudentCardTemplate,
  ): Promise<StudentCardTemplate> {
    try {
      const existingTemplate = await this.find({
        branchId: createCardTemplateData.branchId,
      });

      if (existingTemplate) {
        throw new ConflictException(
          "Student card template already exists for this branch",
        );
      }

      return await executeQueryTakeFirstOrThrow(
        this.db
          .insert(studentCardTemplateTable)
          .values(createCardTemplateData)
          .returning(),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to create student card template", error);
      handleDatabaseInsertException(error, {
        resource: "studentCardTemplate",
      });
    }
  }

  public async update(
    templateId: StudentCardTemplate["id"],
    updateCardTemplateData: StudentCardTemplateUpdate,
  ) {
    try {
      return await executeQueryTakeFirstOrThrow(
        this.db
          .update(studentCardTemplateTable)
          .set(updateCardTemplateData)
          .where(eq(studentCardTemplateTable.id, templateId))
          .returning(),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to update student card template", error);
      handleDatabaseInsertException(error, {
        resource: "studentCardTemplate",
      });
    }
  }

  public async findById(
    id: StudentCardTemplate["id"],
  ): Promise<StudentCardTemplate | undefined> {
    try {
      return await this.db.query.studentCardTemplateTable.findFirst({
        where: eq(studentCardTemplateTable.id, id),
      });
    } catch (error: unknown) {
      this.logger.error("Failed to find student card template by id", error);
      throw error;
    }
  }

  public async find(
    criteria: Partial<StudentCardTemplate>,
  ): Promise<StudentCardTemplate | undefined> {
    const filters: SQL[] = [];

    if (criteria.id) {
      filters.push(eq(studentCardTemplateTable.id, criteria.id));
    }

    if (criteria.branchId) {
      filters.push(eq(studentCardTemplateTable.branchId, criteria.branchId));
    }

    if (criteria.title) {
      filters.push(eq(studentCardTemplateTable.title, criteria.title));
    }

    if (criteria.watermark) {
      filters.push(eq(studentCardTemplateTable.watermark, criteria.watermark));
    }

    if (criteria.headerImage) {
      filters.push(
        eq(studentCardTemplateTable.headerImage, criteria.headerImage),
      );
    }

    if (criteria.backgroundImage) {
      filters.push(
        eq(studentCardTemplateTable.backgroundImage, criteria.backgroundImage),
      );
    }

    if (criteria.createdAt) {
      filters.push(eq(studentCardTemplateTable.createdAt, criteria.createdAt));
    }

    try {
      return await this.db.query.studentCardTemplateTable.findFirst({
        where: and(...filters),
      });
    } catch (error: unknown) {
      this.logger.error("Failed to find student card template", error);
      throw error;
    }
  }
}
