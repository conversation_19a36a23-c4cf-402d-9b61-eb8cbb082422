import { z } from "zod";
import { classSectionResponseSchema } from "../dto/class-sections.dto.js";
import { classSectionTable } from "../../../../db/schemas/education.schema.js";

export type ClassSection = typeof classSectionTable.$inferSelect;
export type NewClassSection = typeof classSectionTable.$inferInsert;

export type ClassSectionUpdate = Partial<NewClassSection>;

export type ClassSectionResponse = z.infer<typeof classSectionResponseSchema>;
