import { ApiOperation, ApiResponse } from "@nestjs/swagger";
import {
  getApiResponseSwaggerSchemaObject,
  useCommonCreateResourceApiResponses,
} from "../../../../docs/shared/api-responses.js";

export function ApiDocCreateClassSection() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Create Class Section",
      description: "Creates  a new class section ",
    }),
    ApiResponse({
      status: 201,
      schema: getApiResponseSwaggerSchemaObject(
        201,
        "Class section created successfully",
      ),
      description:
        "Returns the id of the newly created class section in the Location header",
    }),
  );
}
