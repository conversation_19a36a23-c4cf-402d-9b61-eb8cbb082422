import { z } from "zod";
import { createZodDto } from "nestjs-zod";
import {
  getUuidSchema,
  nameSchema,
} from "../../../../shared/schema/zod-common.schema.js";

const baseSectionSchema = z.object({
  name: nameSchema,
  isActive: z.boolean().default(true),
  classTeacherId: getUuidSchema("Class Teacher ID"),
});

// ------------- Create-Section-Dto ------------->
export const createClassSectionSchema = baseSectionSchema;
export class CreateClassSectionDto extends createZodDto(baseSectionSchema) {}

// ------------- Section-Response-Dto ------------->
export const classSectionResponseSchema = baseSectionSchema
  .omit({
    classTeacherId: true,
  })
  .extend({
    id: z.string(),
    totalStudents: z.number(),
    createdAt: z.date(),
    class: z.object({
      id: z.string(),
      name: z.string(),
    }),
    classTeacher: z.object({
      id: z.string(),
      name: z.string(),
    }),
  });

export class ClassSectionResponseDto extends createZodDto(
  classSectionResponseSchema,
) {}
