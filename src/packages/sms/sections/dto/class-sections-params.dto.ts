import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import { getUuidSchema } from "../../../../shared/schema/zod-common.schema.js";

export class ClassIdParamDto extends createZodDto(
  z.object({ classId: getUuidSchema("classId") }),
) {}

export class ClassSectionParamsDto extends createZodDto(
  z.object({
    classId: getUuidSchema("Class ID"),
    sectionId: getUuidSchema("Section ID"),
  }),
) {}
