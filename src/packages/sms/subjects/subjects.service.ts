import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { Kysely } from "kysely";
import { DB } from "../../../database/types.js";
import { NewSubject, Subject, SubjectUpdate } from "./types/subject.type.js";
import { AcademicSessionsService } from "../academic-sessions/academic-sessions.service.js";
import { AcademicSession } from "../academic-sessions/types/academic-session.types.js";
import { handleDatabaseInsertException } from "../../../utils/pg-utils.js";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import {
  EntityId,
  ListAllEntitiesQueryOptions,
  ServiceOptions,
} from "../../../shared/types/shared.types.js";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../db/type.js";
import { and, desc, eq, SQL } from "drizzle-orm";
import { subjectTable } from "../../../db/schemas/index.js";
import { paginationOptions } from "../../../shared/constants/api-request.constants.js";

@Injectable()
export class SubjectsService {
  private logger = new Logger(SubjectsService.name);

  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    @InjectDrizzle() private readonly db2: Database,
    private readonly academicSessionsService: AcademicSessionsService,
  ) {}

  public async findAll(
    sessionId: AcademicSession["id"],
    queryOptions: ListAllEntitiesQueryOptions,
  ) {
    try {
      const [items, { total }] = await Promise.all([
        this.db
          .selectFrom("subject")
          .selectAll()
          .where("academicSessionId", "=", sessionId)
          .limit(queryOptions.limit)
          .offset(queryOptions.offset)
          .orderBy("createdAt", "desc")
          .execute(),

        this.db
          .selectFrom("subject")
          .select(({ fn }) => [fn.count("subject.id").as("total")])
          .where("academicSessionId", "=", sessionId)
          .executeTakeFirstOrThrow(),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all subjects", error);
      throw error;
    }
  }

  public async findAll2(
    criteria?: Partial<Pick<Subject, "id" | "academicSessionId">>,
    queryOptions?: ListAllEntitiesQueryOptions,
  ) {
    const filters: SQL[] = [];

    if (criteria?.academicSessionId) {
      filters.push(
        eq(subjectTable.academicSessionId, criteria.academicSessionId),
      );
    }

    if (criteria?.id) {
      filters.push(eq(subjectTable.id, criteria.id));
    }

    try {
      return await Promise.all([
        this.db2.query.subjectTable.findMany({
          where: and(...filters),
          limit: queryOptions?.limit ?? paginationOptions.LIMIT,
          offset: queryOptions?.offset ?? paginationOptions.OFFSET,
          orderBy: desc(subjectTable.createdAt),
        }),
      ]);
    } catch (error) {
      this.logger.error("Failed to find all subjects", error);
      throw error;
    }
  }

  public async create(newSubjectPayload: NewSubject): Promise<EntityId> {
    try {
      return await this.db
        .insertInto("subject")
        .values(newSubjectPayload)
        .returning(["id"])
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      handleDatabaseInsertException(error, {
        resource: "subject",
        logger: this.logger,
        messages: {
          uniqueConstraintError: "Subject with same name already exists",
        },
      });
    }
  }

  public async update(
    subjectId: Subject["id"],
    updateSubjectPayload: SubjectUpdate,
  ) {
    try {
      await this.findSubjectOrThrow(subjectId);

      return await this.db
        .updateTable("subject")
        .set(updateSubjectPayload)
        .where("id", "=", subjectId)
        .returningAll()
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      handleDatabaseInsertException(error, {
        resource: "subject",
        logger: this.logger,
      });
    }
  }

  public async findById(id: Subject["id"], options?: ServiceOptions) {
    try {
      const kyselyClient = options?.trx ?? this.db;
      return await kyselyClient
        .selectFrom("subject")
        .selectAll()
        .where("id", "=", id)
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find subject by id", error);
      throw error;
    }
  }

  public async findSubjectOrThrow(subjectId: string, options?: ServiceOptions) {
    let existingSubject: Subject | undefined;

    try {
      existingSubject = await this.findById(subjectId, options);
    } catch (error: unknown) {
      this.logger.error("Failed to verify subject exists", error);
      throw error;
    }
    if (!existingSubject) {
      throw new NotFoundException(`Subject not found`);
    }
    return existingSubject;
  }
}
