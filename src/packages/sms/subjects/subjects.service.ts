import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { Kysely } from "kysely";
import { DB } from "../../../database/types.js";
import { NewSubject, Subject, SubjectUpdate } from "./types/subject.type.js";
import { AcademicSessionsService } from "../academic-sessions/academic-sessions.service.js";
import { AcademicSession } from "../academic-sessions/types/academic-session.types.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertOrUpdateException,
} from "../../../utils/pg-utils.js";
import {
  EntityId,
  ListAllEntitiesQueryOptions,
  ServiceOptions,
} from "../../../shared/types/shared.types.js";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../db/type.js";
import { and, desc, eq, SQL } from "drizzle-orm";
import { subjectTable } from "../../../db/schemas/index.js";

@Injectable()
export class SubjectsService {
  private logger = new Logger(SubjectsService.name);

  public constructor(@InjectDrizzle() private readonly db: Database) {}

  public async findAll(
    sessionId: AcademicSession["id"],
    queryOptions: ListAllEntitiesQueryOptions,
  ) {
    try {
      const [items, total] = await Promise.all([
        this.db.query.subjectTable.findMany({
          where: eq(subjectTable.academicSessionId, sessionId),
          limit: queryOptions.limit,
          offset: queryOptions.offset,
          orderBy: desc(subjectTable.createdAt),
        }),

        this.db.$count(
          subjectTable,
          eq(subjectTable.academicSessionId, sessionId),
        ),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all subjects", error);
      throw error;
    }
  }

  public async create(newSubjectPayload: NewSubject): Promise<EntityId> {
    try {
      return await executeQueryTakeFirstOrThrow(
        this.db
          .insert(subjectTable)
          .values(newSubjectPayload)
          .returning({ id: subjectTable.id }),
      );
    } catch (error: unknown) {
      handleDatabaseInsertOrUpdateException(error, {
        resource: "subject",
        logger: this.logger,
        messages: {
          uniqueConstraintError: "Subject with same name already exists",
        },
      });
    }
  }

  public async update(
    subjectId: Subject["id"],
    updateSubjectPayload: SubjectUpdate,
  ) {
    try {
      await this.findSubjectOrThrow(subjectId);

      return await executeQueryTakeFirstOrThrow(
        this.db
          .update(subjectTable)
          .set(updateSubjectPayload)
          .where(eq(subjectTable.id, subjectId))
          .returning({ id: subjectTable.id }),
      );
    } catch (error: unknown) {
      handleDatabaseInsertOrUpdateException(error, {
        resource: "subject",
        logger: this.logger,
      });
    }
  }

  public async findById(id: Subject["id"], options?: ServiceOptions) {
    try {
      const dbClient = options?.pgTrx ?? this.db;
      return await dbClient.query.subjectTable.findFirst({
        where: eq(subjectTable.id, id),
      });
    } catch (error: unknown) {
      this.logger.error("Failed to find subject by id", error);
      throw error;
    }
  }

  public async findSubjectOrThrow(subjectId: string, options?: ServiceOptions) {
    let existingSubject: Subject | undefined;

    try {
      existingSubject = await this.findById(subjectId, options);
    } catch (error: unknown) {
      this.logger.error("Failed to verify subject exists", error);
      throw error;
    }
    if (!existingSubject) {
      throw new NotFoundException(`Subject not found`);
    }
    return existingSubject;
  }
}
