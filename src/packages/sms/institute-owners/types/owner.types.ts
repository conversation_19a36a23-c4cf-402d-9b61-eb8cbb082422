import { z } from "zod";
import { createOwnerSchema, ownerResponseSchema } from "../dto/owners.dto.js";
import { instituteOwnerTable } from "../../../../db/schemas/institute.schema.js";

export type InstituteOwner = typeof instituteOwnerTable.$inferSelect;
export type NewInstituteOwner = typeof instituteOwnerTable.$inferInsert;
export type InstituteOwnerUpdate = Partial<NewInstituteOwner>;

export type CreateOwnerPayload = z.infer<typeof createOwnerSchema>;

export type InstituteOwnerProfile = z.infer<typeof ownerResponseSchema>;

// Criteria to find unique owner
export type UniqueOwnerCriteria = Partial<
  Pick<InstituteOwnerProfile, "email" | "cnic" | "phone" | "createdAt"> & {
    userId: string;
  }
>;
