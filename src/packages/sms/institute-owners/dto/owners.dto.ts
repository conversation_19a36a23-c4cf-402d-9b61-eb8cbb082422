import {
  createUserSchema,
  userProfileSchema,
} from "../../../../core/users/dto/user.dto.js";
import { createZodDto } from "nestjs-zod";

// ------------------- Create-Owner-Dto ------------------->
export const createOwnerSchema = createUserSchema;
export class CreateOwnerDto extends createZodDto(createOwnerSchema) {}

// ------------------- Update-Owner-Dto ------------------->
export const updateOwnerSchema = createOwnerSchema.partial();
export class UpdateOwnerDto extends createZodDto(updateOwnerSchema) {}

// ------------------- Get-Owner-Dto ------------------->
export const ownerResponseSchema = userProfileSchema;
export class OwnerResponseDto extends createZodDto(ownerResponseSchema) {}
