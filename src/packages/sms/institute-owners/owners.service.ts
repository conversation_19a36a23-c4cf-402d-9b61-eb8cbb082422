import {
  Injectable,
  Logger,
  NotFoundException,
  OnModuleInit,
} from "@nestjs/common";
import { UsersService } from "../../../core/users/users.service.js";
import { RolesService } from "../../../core/roles/roles.service.js";
import { ListAllEntitiesQueryOptions } from "../../../shared/types/shared.types.js";
import {
  CreateOwnerPayload,
  InstituteOwner,
  InstituteOwnerProfile,
  InstituteOwnerUpdate,
  UniqueOwnerCriteria,
} from "./types/owner.types.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertOrUpdateException,
} from "../../../utils/pg-utils.js";
import { ModuleRef } from "@nestjs/core";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../db/type.js";
import {
  instituteOwnerTable,
  roleTable,
  usersTable,
} from "../../../db/schemas/index.js";
import { eq, SQL } from "drizzle-orm";

@Injectable()
export class OwnersService implements OnModuleInit {
  private readonly logger = new Logger(OwnersService.name);
  private usersService: UsersService;

  public constructor(
    @InjectDrizzle() private readonly db: Database,
    private readonly rolesService: RolesService,
    private readonly moduleRef: ModuleRef,
  ) {}

  public onModuleInit() {
    this.usersService = this.moduleRef.get(UsersService, { strict: false });
  }

  public async findAll({
    limit,
    offset,
  }: ListAllEntitiesQueryOptions): Promise<Array<InstituteOwnerProfile>> {
    try {
      return await this.findOwnerQuery().offset(offset).limit(limit).execute();
    } catch (error: unknown) {
      this.logger.error("Failed to get all owners", error);
      throw error;
    }
  }

  public async update(
    id: InstituteOwner["userId"],
    data: InstituteOwnerUpdate,
  ) {
    try {
      await this.db
        .update(instituteOwnerTable)
        .set(data)
        .where(eq(instituteOwnerTable.userId, id));
    } catch (error: unknown) {
      this.logger.error("Failed to update owner", error);
      throw error;
    }
  }

  public async findUniqueOwner(
    criteria: Partial<Pick<InstituteOwner, "userId">>,
  ): Promise<InstituteOwnerProfile | undefined> {
    try {
      const owners = await this.findOwnerQuery(criteria).execute();
      return owners[0];
    } catch (error: unknown) {
      this.logger.error("Failed to find unique owner", error);
      throw error;
    }
  }

  /**
   * Creates a new owner and sends email with temporary credentials
   * @returns Newly create owner profile
   */
  public async create(
    createOwnerInput: CreateOwnerPayload,
  ): Promise<InstituteOwnerProfile> {
    try {
      return await this.db.transaction(async pgTrx => {
        const instituteOwnerRole = await this.rolesService.findByNameOrCreate(
          "INSTITUTE_OWNER",
          { pgTrx },
        );

        // create new user
        const newUser = await this.usersService.create(
          {
            ...createOwnerInput,
            roleId: instituteOwnerRole.id,
            isPasswordTemporary: true,
          },
          { pgTrx },
        );

        // create new owner
        await executeQueryTakeFirstOrThrow(
          pgTrx
            .insert(instituteOwnerTable)
            .values({
              userId: newUser.id,
            })
            .returning(),
        );

        return newUser;
      });
    } catch (error: unknown) {
      this.logger.error("Failed to create owner", error);
      handleDatabaseInsertOrUpdateException(error, {
        resource: "instituteOwner",
        logger: this.logger,
      });
    }
  }

  public async findFirstOrThrow(
    criteria: UniqueOwnerCriteria,
  ): Promise<InstituteOwnerProfile> {
    const owners = await this.findOwnerQuery(criteria).execute();
    if (!owners[0]) {
      throw new NotFoundException("Owner not found");
    }
    return owners[0];
  }

  private findOwnerQuery(criteria?: UniqueOwnerCriteria) {
    const filters: SQL[] = [];

    if (criteria?.email) {
      filters.push(eq(usersTable.email, criteria.email));
    }

    if (criteria?.cnic) {
      filters.push(eq(usersTable.cnic, criteria.cnic));
    }

    if (criteria?.phone) {
      filters.push(eq(usersTable.phone, criteria.phone));
    }
    if (criteria?.userId) {
      filters.push(eq(instituteOwnerTable.userId, criteria.userId));
    }

    if (criteria?.createdAt) {
      filters.push(eq(instituteOwnerTable.createdAt, criteria.createdAt));
    }

    return this.db
      .select({
        id: usersTable.id,
        role: roleTable.code,
        email: usersTable.email,
        name: usersTable.name,
        gender: usersTable.gender,
        address: usersTable.address,
        phone: usersTable.phone,
        cnic: usersTable.cnic,
        isActive: usersTable.isActive,
        photo: usersTable.photo,
        isPasswordTemporary: usersTable.isPasswordTemporary,
        createdAt: instituteOwnerTable.createdAt,
      })
      .from(instituteOwnerTable)
      .innerJoin(usersTable, eq(instituteOwnerTable.userId, usersTable.id))
      .innerJoin(roleTable, eq(usersTable.roleId, roleTable.id));
  }
}
