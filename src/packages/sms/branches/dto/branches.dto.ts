import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import { instituteResponseSchema } from "../../institutes/dto/institute.dto.js";

const baseBranchSchema = z.object({
  name: z.string(),
  address: z.string(),
  phone: z.string(),
  email: z.string().email(),
  isMain: z.boolean().default(false),
  isActive: z.boolean().default(true),
});

// ------------------- Create-Branch-Dto ------------------->
export class CreateBranchDto extends createZodDto(baseBranchSchema) {}

// ------------------- Branch-Response-Dto ------------------->
export const branchResponseSchema = baseBranchSchema.extend({
  id: z.string(),
  institute: instituteResponseSchema,
  createdAt: z.date(),
});

export class BranchResponseDto extends createZodDto(branchResponseSchema) {}
