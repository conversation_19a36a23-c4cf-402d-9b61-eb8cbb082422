import { ApiOperation, ApiResponse } from "@nestjs/swagger";
import { BranchResponseDto } from "../dto/branches.dto.js";
import { applyDecorators } from "@nestjs/common";
import { useCommonCreateResourceApiResponses } from "../../../../docs/shared/api-responses.js";

export function ApiDocGetAllBranches() {
  return applyDecorators(
    ApiOperation({
      summary: "Get all branches",
      description: "Returns all branches of the institute",
    }),
    ApiResponse({
      status: 200,
      type: BranchResponseDto,
      isArray: true,
      description: "Returns the list of branches",
    }),
  );
}

export function ApiDocCreateBranch() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Create a new Branch",
      description: "Creates a new branch with the provided details",
    }),
    ApiResponse({
      status: 201,
      type: BranchResponseDto,
      description: "Returns the newly created branch with complete details",
    }),
  );
}
