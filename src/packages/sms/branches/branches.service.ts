import { Injectable, Logger } from "@nestjs/common";
import { ListAllEntitiesQueryOptions } from "../../../shared/types/shared.types.js";
import { Branch, BranchResponse, NewBranch } from "./types/branches.types.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertOrUpdateException,
} from "../../../utils/pg-utils.js";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../db/type.js";
import { branchTable } from "../../../db/schemas/index.js";
import { and, eq, SQL } from "drizzle-orm";

@Injectable()
export class BranchesService {
  public constructor(@InjectDrizzle() private readonly db: Database) {}

  private readonly logger = new Logger(BranchesService.name);

  public async findAll(
    instituteId: Branch["instituteId"],
    { limit, offset }: ListAllEntitiesQueryOptions,
  ): Promise<BranchResponse[]> {
    try {
      return await this.db.query.branchTable.findMany({
        where: eq(branchTable.instituteId, instituteId),
        columns: {
          instituteId: false,
        },
        with: {
          institute: true,
        },
        limit,
        offset,
      });
    } catch (error: unknown) {
      this.logger.error("Failed to find all branches", error);
      throw error;
    }
  }

  public async create(data: NewBranch): Promise<Branch> {
    try {
      return await executeQueryTakeFirstOrThrow(
        this.db.insert(branchTable).values(data).returning(),
      );
    } catch (error) {
      handleDatabaseInsertOrUpdateException(error, {
        resource: "branch",
        logger: this.logger,
      });
    }
  }

  public async findOne(
    criteria: Partial<Pick<Branch, "name" | "id" | "instituteId">>,
  ): Promise<BranchResponse | undefined> {
    try {
      return await this.db.query.branchTable.findFirst({
        where: and(...this.createQueryFilters(criteria)),
        columns: {
          instituteId: false,
        },
        with: {
          institute: true,
        },
      });
    } catch (error: unknown) {
      this.logger.error("Failed to find unique branch", error);
      throw error;
    }
  }

  public async findById(id: Branch["id"]): Promise<BranchResponse | undefined> {
    try {
      return await this.findOne({ id });
    } catch (error: unknown) {
      this.logger.error("Failed to find unique branch", error);
      throw error;
    }
  }

  public async findBranchByName(
    branchName: Branch["name"],
  ): Promise<BranchResponse | undefined> {
    try {
      return await this.findOne({
        name: branchName,
      });
    } catch (error: unknown) {
      this.logger.error("Failed to find unique branch", error);
      throw error;
    }
  }

  private createQueryFilters(
    criteria?: Partial<Pick<Branch, "name" | "id" | "instituteId">>,
  ) {
    const filters: SQL[] = [];

    if (criteria?.name) {
      filters.push(eq(branchTable.name, criteria.name));
    }
    if (criteria?.id) {
      filters.push(eq(branchTable.id, criteria.id));
    }

    if (criteria?.instituteId) {
      filters.push(eq(branchTable.instituteId, criteria.instituteId));
    }

    return filters;
  }
}
