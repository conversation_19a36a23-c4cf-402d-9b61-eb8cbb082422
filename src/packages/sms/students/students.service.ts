import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { CreateStudentPayload, Student } from "./types/students.types.js";
import { GuardiansService } from "../guardians/guardians.service.js";
import { AcademicSessionsService } from "../academic-sessions/academic-sessions.service.js";
import { AcademicSession } from "../academic-sessions/types/academic-session.types.js";
import { extractKeysFromObject } from "../../../utils/object-utils.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertOrUpdateException,
} from "../../../utils/pg-utils.js";
import { EnrollmentService } from "./enrollments/enrollments.service.js";
import {
  EntityId,
  ServiceOptions,
} from "../../../shared/types/shared.types.js";
import { ListStudentsQueryDto } from "./dto/students-query.dto.js";
import { ClassSectionsService } from "../sections/class-sections.service.js";
import { ClassSection } from "../sections/types/class-sections.types.js";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../db/type.js";
import {
  enrollmentTable,
  studentTable,
} from "../../../db/schemas/student.schema.js";
import { classTable } from "../../../db/schemas/academic.schema.js";
import { classSectionTable } from "../../../db/schemas/education.schema.js";
import { and, eq, desc, count, SQL } from "drizzle-orm";

@Injectable()
export class StudentsService {
  private readonly logger = new Logger(StudentsService.name);

  public constructor(
    @InjectDrizzle() private readonly db: Database,
    private readonly academicSessionsService: AcademicSessionsService,
    private readonly guardiansService: GuardiansService,
    private readonly classSectionsService: ClassSectionsService,
    private readonly enrollmentsService: EnrollmentService,
  ) {}

  public async create(
    createStudentPayload: CreateStudentPayload,
  ): Promise<EntityId> {
    await this.verifyAcademicSessionExists(
      createStudentPayload.academicSessionId,
    );

    try {
      const enrolledStudentCount = await this.db.$count(
        enrollmentTable,
        and(
          eq(
            enrollmentTable.classSectionId,
            createStudentPayload.classSectionId,
          ),
          eq(enrollmentTable.status, "ACTIVE"),
        ),
      );

      const guardianInfo = this.extractGuardianInfo(createStudentPayload);

      return await this.db.transaction(async trx => {
        const guardian = await this.guardiansService.create(guardianInfo, {
          pgTrx: trx,
        });

        const studentInfo = this.extractStudentInfo(createStudentPayload);

        const { id: studentId } = await executeQueryTakeFirstOrThrow(
          trx
            .insert(studentTable)
            .values({
              ...studentInfo,
              rollNumber: enrolledStudentCount + 1,
              guardianId: guardian.id,
            })
            .returning({ id: studentTable.id }),
        );
        await this.enrollStudent(studentId, createStudentPayload, {
          pgTrx: trx,
        });
        return { id: studentId };
      });
    } catch (error: unknown) {
      this.logger.error("Failed to create student");
      handleDatabaseInsertOrUpdateException(error, {
        resource: "student",
        logger: this.logger,
      });
    }
  }

  public async findAllByAcademicSessionId(
    sessionId: AcademicSession["id"],
    filterOptions: ListStudentsQueryDto,
  ) {
    try {
      const filters = this.buildQueryFilters({
        academicSessionId: sessionId,
        classId: filterOptions.classId,
        sectionId: filterOptions.sectionId,
        enrollmentStatus: filterOptions.enrollmentStatus,
        enrollmentType: filterOptions.enrollmentType,
      });

      const [items, total] = await Promise.all([
        this.buildStudentSelectQuery(filters)
          .limit(filterOptions.limit)
          .offset(filterOptions.offset)
          .orderBy(desc(studentTable.createdAt)),

        this.buildStudentCountQuery(filters),
      ]);

      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all students", error);
      throw error;
    }
  }

  public async findAllByClassSectionId(classSectionId: ClassSection["id"]) {
    try {
      const filters = this.buildQueryFilters({
        sectionId: classSectionId,
      });

      const [items, total] = await Promise.all([
        this.buildStudentSelectQuerySimple(filters),
        this.buildStudentCountQuerySimple(filters),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all students", error);
      throw error;
    }
  }

  public async findById(id: Student["id"]) {
    try {
      const result = await this.db
        .select()
        .from(studentTable)
        .where(eq(studentTable.id, id))
        .limit(1);

      return result[0] ?? undefined;
    } catch (error: unknown) {
      this.logger.error("Failed to find student by id", error);
      throw error;
    }
  }

  private async enrollStudent(
    studentId: Student["id"],
    createStudentPayload: CreateStudentPayload,
    options: ServiceOptions,
  ) {
    const enrollmentPayload = {
      studentId,
      classSectionId: createStudentPayload.classSectionId,
      academicSessionId: createStudentPayload.academicSessionId,
      type: "ADMISSION",
      status: "ACTIVE",
      date: createStudentPayload.admissionDate,
    } as const;

    try {
      await this.enrollmentsService.create(enrollmentPayload, options);
    } catch (error: unknown) {
      this.logger.error("Failed to enroll student");
      throw error;
    }
  }

  private async verifyAcademicSessionExists(
    academicSessionId: AcademicSession["id"],
  ) {
    let existingAcademicSession: AcademicSession | undefined;
    try {
      existingAcademicSession =
        await this.academicSessionsService.findById(academicSessionId);
    } catch (error: unknown) {
      this.logger.log("Failed to verify academic session exists", error);
      throw error;
    }

    if (!existingAcademicSession) {
      throw new NotFoundException(
        `Academic session with id: ${academicSessionId} not found`,
      );
    }
  }

  private extractStudentInfo(createStudentPayload: CreateStudentPayload) {
    return extractKeysFromObject(createStudentPayload, [
      "name",
      "registrationNumber",
      "email",
      "address",
      "gender",
      "photo",
      "monthlyFee",
      "admissionDate",
      "dateOfBirth",
      "classSectionId",
      "fatherName",
      "previousSchool",
      "religion",
    ]);
  }

  private extractGuardianInfo(createStudentPayload: CreateStudentPayload) {
    const {
      guardianAddress: address,
      guardianEmail: email,
      guardianName: name,
      guardianPhone: phone,
      guardianRelation: relation,
      guardianCnic: cnic,
      guardianGender: gender,
      guardianPassword: password,
    } = createStudentPayload;
    return { address, email, gender, name, phone, relation, cnic, password };
  }

  /**
   * Builds the base select query for students with all necessary joins
   */
  private buildStudentSelectQuery(filters: SQL[]) {
    return this.db
      .select({
        id: studentTable.id,
        name: studentTable.name,
        registrationNumber: studentTable.registrationNumber,
        rollNumber: studentTable.rollNumber,
        email: studentTable.email,
        fatherName: studentTable.fatherName,
        address: studentTable.address,
        gender: studentTable.gender,
        photo: studentTable.photo,
        religion: studentTable.religion,
        monthlyFee: studentTable.monthlyFee,
        admissionDate: studentTable.admissionDate,
        dateOfBirth: studentTable.dateOfBirth,
        previousSchool: studentTable.previousSchool,
        classSectionId: studentTable.classSectionId,
        guardianId: studentTable.guardianId,
        createdAt: studentTable.createdAt,
        enrollmentId: enrollmentTable.id,
      })
      .from(studentTable)
      .innerJoin(
        enrollmentTable,
        eq(enrollmentTable.studentId, studentTable.id),
      )
      .innerJoin(
        classSectionTable,
        eq(classSectionTable.id, enrollmentTable.classSectionId),
      )
      .innerJoin(classTable, eq(classTable.id, classSectionTable.classId))
      .where(and(...filters));
  }

  /**
   * Builds the count query for students with the same joins as select query
   */
  private buildStudentCountQuery(filters: SQL[]) {
    return this.db
      .select({ count: count() })
      .from(studentTable)
      .innerJoin(
        enrollmentTable,
        eq(enrollmentTable.studentId, studentTable.id),
      )
      .innerJoin(
        classSectionTable,
        eq(classSectionTable.id, enrollmentTable.classSectionId),
      )
      .innerJoin(classTable, eq(classTable.id, classSectionTable.classId))
      .where(and(...filters))
      .then(result => result[0]?.count ?? 0);
  }

  /**
   * Builds a simple select query for students with only enrollment join (for class section queries)
   */
  private buildStudentSelectQuerySimple(filters: SQL[]) {
    return this.db
      .select({
        id: studentTable.id,
        name: studentTable.name,
        registrationNumber: studentTable.registrationNumber,
        rollNumber: studentTable.rollNumber,
        email: studentTable.email,
        fatherName: studentTable.fatherName,
        address: studentTable.address,
        gender: studentTable.gender,
        photo: studentTable.photo,
        religion: studentTable.religion,
        monthlyFee: studentTable.monthlyFee,
        admissionDate: studentTable.admissionDate,
        dateOfBirth: studentTable.dateOfBirth,
        previousSchool: studentTable.previousSchool,
        classSectionId: studentTable.classSectionId,
        guardianId: studentTable.guardianId,
        createdAt: studentTable.createdAt,
        enrollmentId: enrollmentTable.id,
      })
      .from(studentTable)
      .innerJoin(
        enrollmentTable,
        eq(enrollmentTable.studentId, studentTable.id),
      )
      .where(and(...filters));
  }

  /**
   * Builds a simple count query for students with only enrollment join (for class section queries)
   */
  private buildStudentCountQuerySimple(filters: SQL[]) {
    return this.db
      .select({ count: count() })
      .from(studentTable)
      .innerJoin(
        enrollmentTable,
        eq(enrollmentTable.studentId, studentTable.id),
      )
      .where(and(...filters))
      .then(result => result[0]?.count ?? 0);
  }

  /**
   * Builds query filters based on filter options
   */
  private buildQueryFilters(filterOptions: {
    academicSessionId?: string;
    classId?: string;
    sectionId?: string;
    enrollmentStatus?:
      | "ACTIVE"
      | "EXPELLED"
      | "GRADUATED"
      | "DECEASED"
      | "COMPLETED"
      | "WITHDRAWN";
    enrollmentType?: "ADMISSION" | "TRANSFER_IN" | "REPEATING" | "PROMOTION";
  }) {
    const filters: SQL[] = [];

    if (filterOptions.academicSessionId) {
      filters.push(
        eq(enrollmentTable.academicSessionId, filterOptions.academicSessionId),
      );
    }

    if (filterOptions.classId) {
      filters.push(eq(classTable.id, filterOptions.classId));
    }

    if (filterOptions.sectionId) {
      filters.push(eq(enrollmentTable.classSectionId, filterOptions.sectionId));
    }

    if (filterOptions.enrollmentStatus) {
      filters.push(eq(enrollmentTable.status, filterOptions.enrollmentStatus));
    }

    if (filterOptions.enrollmentType) {
      filters.push(eq(enrollmentTable.type, filterOptions.enrollmentType));
    }

    return filters;
  }
}
