import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { Kysely } from "kysely";
import { DB } from "../../../database/types.js";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { CreateStudentPayload, Student } from "./types/students.types.js";
import { GuardiansService } from "../guardians/guardians.service.js";
import { AcademicSessionsService } from "../academic-sessions/academic-sessions.service.js";
import { AcademicSession } from "../academic-sessions/types/academic-session.types.js";
import { extractKeysFromObject } from "../../../utils/object-utils.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertException,
} from "../../../utils/pg-utils.js";
import { EnrollmentService } from "./enrollments/enrollments.service.js";
import {
  EntityId,
  ServiceOptions,
} from "../../../shared/types/shared.types.js";
import { ListStudentsQueryDto } from "./dto/students-query.dto.js";
import { ClassSectionsService } from "../sections/class-sections.service.js";
import {
  ClassSection,
  ClassSectionResponse,
} from "../sections/types/class-sections.types.js";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../db/type.js";
import {
  enrollmentTable,
  studentTable,
} from "../../../db/schemas/student.schema.js";
import { and, eq } from "drizzle-orm";

@Injectable()
export class StudentsService {
  private readonly logger = new Logger(StudentsService.name);

  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    @InjectDrizzle() private readonly db2: Database,
    private readonly academicSessionsService: AcademicSessionsService,
    private readonly guardiansService: GuardiansService,
    private readonly classSectionsService: ClassSectionsService,
    private readonly enrollmentsService: EnrollmentService,
  ) {}

  public async create(
    createStudentPayload: CreateStudentPayload,
  ): Promise<EntityId> {
    await this.verifyAcademicSessionExists(
      createStudentPayload.academicSessionId,
    );

    await this.verifyClassSectionExists(createStudentPayload.classSectionId);

    try {
      const enrolledStudentCount = await this.db2.$count(
        enrollmentTable,
        and(
          eq(
            enrollmentTable.classSectionId,
            createStudentPayload.classSectionId,
          ),
          eq(enrollmentTable.status, "ACTIVE"),
        ),
      );

      const guardianInfo = this.extractGuardianInfo(createStudentPayload);

      return await this.db2.transaction(async trx => {
        const guardian = await this.guardiansService.create(guardianInfo, {
          pgTrx: trx,
        });

        const studentInfo = this.extractStudentInfo(createStudentPayload);

        const { id: studentId } = await executeQueryTakeFirstOrThrow(
          trx
            .insert(studentTable)
            .values({
              ...studentInfo,
              rollNumber: enrolledStudentCount + 1,
              guardianId: guardian.id,
            })
            .returning({ id: studentTable.id }),
        );
        await this.enrollStudent(studentId, createStudentPayload, {
          pgTrx: trx,
        });
        return { id: studentId };
      });
    } catch (error: unknown) {
      this.logger.error("Failed to create student");
      handleDatabaseInsertException(error, {
        resource: "student",
        logger: this.logger,
      });
    }
  }

  public async findAllByAcademicSessionId(
    sessionId: AcademicSession["id"],
    filterOptions: ListStudentsQueryDto,
  ) {
    try {
      let baseQuery = this.db
        .selectFrom("student")
        .innerJoin("enrollment", "enrollment.studentId", "student.id")
        .innerJoin(
          "classSection",
          "classSection.id",
          "enrollment.classSectionId",
        )
        .innerJoin("class", "class.id", "classSection.classId")
        .where("enrollment.academicSessionId", "=", sessionId);
      if (filterOptions.classId) {
        baseQuery = baseQuery.where("class.id", "=", filterOptions.classId);
      }

      if (filterOptions.sectionId) {
        baseQuery = baseQuery.where(
          "enrollment.classSectionId",
          "=",
          filterOptions.sectionId,
        );
      }

      if (filterOptions.enrollmentStatus) {
        baseQuery = baseQuery.where(
          "enrollment.status",
          "=",
          filterOptions.enrollmentStatus,
        );
      }

      if (filterOptions.enrollmentType) {
        baseQuery = baseQuery.where(
          "enrollment.type",
          "=",
          filterOptions.enrollmentType,
        );
      }

      const [items, { total }] = await Promise.all([
        baseQuery
          .selectAll("student")
          .select(["enrollment.id as enrollmentId"])
          .limit(filterOptions.limit)
          .offset(filterOptions.offset)
          .orderBy("student.createdAt", "desc")
          .execute(),

        baseQuery
          .select(({ fn }) => [fn.count("student.id").as("total")])
          .executeTakeFirstOrThrow(),
      ]);

      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all students", error);
      throw error;
    }
  }

  public async findAllByClassSectionId(classSectionId: ClassSection["id"]) {
    const baseQuery = this.db
      .selectFrom("student")
      .innerJoin("enrollment", "enrollment.studentId", "student.id")
      .where("enrollment.classSectionId", "=", classSectionId);
    try {
      const [items, { total }] = await Promise.all([
        baseQuery
          .selectAll("student")
          .select(["enrollment.id as enrollmentId"])
          .execute(),

        baseQuery
          .select(({ fn }) => [fn.count("student.id").as("total")])
          .executeTakeFirstOrThrow(),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all students", error);
      throw error;
    }
  }

  public async findById(id: Student["id"]) {
    try {
      return await this.db
        .selectFrom("student")
        .selectAll()
        .where("id", "=", id)
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find student by id", error);
      throw error;
    }
  }

  private async enrollStudent(
    studentId: Student["id"],
    createStudentPayload: CreateStudentPayload,
    options: ServiceOptions,
  ) {
    const enrollmentPayload = {
      studentId,
      classSectionId: createStudentPayload.classSectionId,
      academicSessionId: createStudentPayload.academicSessionId,
      type: "ADMISSION",
      status: "ACTIVE",
      date: createStudentPayload.admissionDate,
    } as const;

    try {
      await this.enrollmentsService.create(enrollmentPayload, options);
    } catch (error: unknown) {
      this.logger.error("Failed to enroll student");
      throw error;
    }
  }

  private async verifyClassSectionExists(classSectionId: ClassSection["id"]) {
    let existingClassSection: ClassSectionResponse | undefined;
    try {
      existingClassSection =
        await this.classSectionsService.findById(classSectionId);
    } catch (error: unknown) {
      this.logger.log("Failed to verify class section exists", error);
      throw error;
    }

    if (!existingClassSection) {
      throw new NotFoundException(
        `Class section with id: ${classSectionId} not found`,
      );
    }
  }

  private async verifyAcademicSessionExists(
    academicSessionId: AcademicSession["id"],
  ) {
    let existingAcademicSession: AcademicSession | undefined;
    try {
      existingAcademicSession =
        await this.academicSessionsService.findById(academicSessionId);
    } catch (error: unknown) {
      this.logger.log("Failed to verify academic session exists", error);
      throw error;
    }

    if (!existingAcademicSession) {
      throw new NotFoundException(
        `Academic session with id: ${academicSessionId} not found`,
      );
    }
  }

  private extractStudentInfo(createStudentPayload: CreateStudentPayload) {
    return extractKeysFromObject(createStudentPayload, [
      "name",
      "registrationNumber",
      "email",
      "address",
      "gender",
      "photo",
      "monthlyFee",
      "admissionDate",
      "dateOfBirth",
      "classSectionId",
      "fatherName",
      "previousSchool",
      "religion",
    ]);
  }

  private extractGuardianInfo(createStudentPayload: CreateStudentPayload) {
    const {
      guardianAddress: address,
      guardianEmail: email,
      guardianName: name,
      guardianPhone: phone,
      guardianRelation: relation,
      guardianCnic: cnic,
      guardianGender: gender,
      guardianPassword: password,
    } = createStudentPayload;
    return { address, email, gender, name, phone, relation, cnic, password };
  }
}
