import { z } from "zod";
import {
  addressSchema,
  cnicSchema,
  pgDateSchema,
  genderSchema,
  getUuidSchema,
  imageUrlSchema,
  nameSchema,
  passwordSchema,
  phoneNumberSchema,
  religionSchema,
} from "../../../../shared/schema/zod-common.schema.js";
import { createZodDto } from "nestjs-zod";
import { createPaginatedResourceResponseSchema } from "../../../../shared/schema/api-response.schema.js";

// ------------------- Student-Base-Schema ------------------->
export const studentBaseSchema = z.object({
  name: nameSchema,
  fatherName: nameSchema,
  religion: religionSchema,
  registrationNumber: z
    .string()
    .nonempty()
    .max(100, {
      message: "Registration number must be less than 100 characters",
    })
    .optional(),
  email: z
    .string()
    .email({ message: "Invalid email address" })
    .max(100, { message: "Email must be less than 100 characters" })
    .optional(),
  address: addressSchema,
  gender: genderSchema,
  photo: imageUrlSchema.optional(),
  monthlyFee: z.number().positive().max(1_000_000, {
    message: "Monthly fee cannot exceed 1,000,000",
  }),
  admissionDate: pgDateSchema,
  dateOfBirth: pgDateSchema,
  previousSchool: z
    .string()
    .max(100, {
      message: "Previous school name must be less than 100 characters",
    })
    .optional(),
  guardianName: nameSchema,
  guardianPhone: phoneNumberSchema,
  guardianEmail: z.string().email().max(100, {
    message: "Email must be less than 100 characters",
  }),
  guardianPassword: passwordSchema,
  guardianAddress: addressSchema,
  guardianGender: genderSchema,
  guardianRelation: z.enum(["FATHER", "MOTHER", "GUARDIAN"], {
    message:
      "Guardian relation must be one of 'FATHER', 'MOTHER', or 'GUARDIAN'.",
  }),
  guardianCnic: cnicSchema,
  classSectionId: getUuidSchema("Class Section ID"),
});

// ------------------- Create-Student-Dto ------------------->
export const createStudentSchema = studentBaseSchema;
export class CreateStudentDto extends createZodDto(studentBaseSchema) {}

// ------------------- Get-Student-Dto ------------------->
export const studentResponseSchema = studentBaseSchema.extend({
  id: getUuidSchema("Student ID"),
  rollNumber: z.number(),
  createdAt: z.date(),
  enrollmentId: getUuidSchema("Enrollment ID"),
});
export class StudentResponseDto extends createZodDto(
  createPaginatedResourceResponseSchema(studentResponseSchema),
) {}
