import { createZodDto } from "nestjs-zod";
import { listAllEntitiesQuerySchema } from "../../../../../shared/schema/zod-common.schema.js";
import {
  enrollmentStatusSchema,
  enrollmentTypeSchema,
} from "./enrollments.dto.js";
import { z } from "zod";

const listEnrollmentsQuerySchema = listAllEntitiesQuerySchema.extend({
  studentId: z.string().optional(),
  classId: z.string().optional(),
  sectionId: z.string().optional(),
  academicSessionId: z.string().optional(),
  type: enrollmentTypeSchema.optional(),
  status: enrollmentStatusSchema.optional(),
});

export class ListEnrollmentsQueryDto extends createZodDto(
  listEnrollmentsQuerySchema,
) {}
