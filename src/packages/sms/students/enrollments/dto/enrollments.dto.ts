import { z } from "zod";
import {
  pgDateSchema,
  getUuidSchema,
} from "../../../../../shared/schema/zod-common.schema.js";
import { createZodDto } from "nestjs-zod";
import { classSectionResponseSchema } from "../../../sections/dto/class-sections.dto.js";

export const enrollmentStatusSchema = z.enum(
  ["ACTIVE", "EXPELLED", "GRADUATED", "DECEASED", "COMPLETED", "WITHDRAWN"],
  {
    message:
      "Enrollment status must be one of 'ACTIVE', 'EXPELLED', 'GRADUATED', 'DECEASED', 'COMPLETED', or 'WITHDRAWN'.",
  },
);
export const enrollmentTypeSchema = z.enum(
  ["ADMISSION", "TRANSFER_IN", "REPEATING", "PROMOTION"],
  {
    message:
      "Enrollment type must be one of 'ADMISSION', 'TRANSFER_IN', 'REPEATING', or 'PROMOTION'.",
  },
);
export const enrollmentBaseSchema = z.object({
  studentId: getUuidSchema("Student ID"),
  classSectionId: getUuidSchema("Class Section ID"),
  academicSessionId: getUuidSchema("Academic Session ID"),
  type: enrollmentTypeSchema,
  status: enrollmentStatusSchema,
  date: pgDateSchema,
});

// ------------------- Create-Enrollment-Dto ------------------->
const createEnrollmentSchema = enrollmentBaseSchema.omit({
  academicSessionId: true,
});
export class CreateEnrollmentDto extends createZodDto(createEnrollmentSchema) {}

// ------------------- Get-Enrollment-Dto ------------------->
export const enrollmentResponseSchema = enrollmentBaseSchema
  .omit({ academicSessionId: true, studentId: true, classSectionId: true })
  .extend({
    id: getUuidSchema("Enrollment ID"),
    classSection: classSectionResponseSchema,
    academicSessionName: z.string(),
    student: z.object({
      id: z.string(),
      name: z.string(),
      rollNumber: z.number(),
    }),
    createdAt: z.date(),
  });
export class EnrollmentResponseDto extends createZodDto(
  enrollmentResponseSchema,
) {}
