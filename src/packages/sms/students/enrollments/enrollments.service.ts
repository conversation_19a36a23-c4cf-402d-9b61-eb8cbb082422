import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { ServiceOptions } from "../../../../shared/types/shared.types.js";
import {
  Enrollment,
  EnrollmentResponse,
  NewEnrollment,
} from "./types/enrollment.types.js";
import { handleDatabaseInsertException } from "../../../../utils/pg-utils.js";
import { InjectDrizzle } from "../../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../../db/type.js";
import {
  enrollmentTable,
  studentTable,
} from "../../../../db/schemas/student.schema.js";
import { executeQueryTakeFirstOrThrow } from "../../../../utils/pg-utils.js";
import { and, eq, sql, SQL } from "drizzle-orm";
import {
  academicSessionTable,
  classSectionTable,
  classTable,
} from "../../../../db/schemas/index.js";
import { paginationOptions } from "../../../../shared/constants/api-request.constants.js";
import { getSelectClassSectionSqlQuery } from "../../../../utils/drizzle-raw-sql-queries.utils.js";
import { ListEnrollmentsQueryDto } from "./dto/enrollments-query.dto.js";
import { PaginatedApiResponse } from "../../../../shared/types/shared.types.js";

@Injectable()
export class EnrollmentService {
  private readonly logger = new Logger(EnrollmentService.name);
  public constructor(@InjectDrizzle() private readonly db: Database) {}

  public async create(
    newEnrollmentData: NewEnrollment,
    options?: ServiceOptions,
  ): Promise<Enrollment> {
    const dbClient = options?.pgTrx ?? this.db;
    try {
      return await executeQueryTakeFirstOrThrow(
        dbClient.insert(enrollmentTable).values(newEnrollmentData).returning(),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to create enrollment", error);
      handleDatabaseInsertException(error, {
        resource: "enrollment",
        logger: this.logger,
      });
    }
  }

  public async findFirstOrThrow(
    criteria: Partial<Pick<Enrollment, "id" | "academicSessionId">>,
  ): Promise<EnrollmentResponse> {
    let existingEnrollment: EnrollmentResponse | undefined;
    try {
      existingEnrollment = (
        await this.selectEnrollmentQuery(this.getFilters(criteria)).execute()
      )[0];
    } catch (error: unknown) {
      this.logger.error("Failed to find enrollment", error);
      throw error;
    }
    if (!existingEnrollment) {
      throw new NotFoundException(`Enrollment not found`);
    }
    return existingEnrollment;
  }

  public async findById(
    id: Enrollment["id"],
  ): Promise<EnrollmentResponse | undefined> {
    try {
      const res = await this.selectEnrollmentQuery(
        this.getFilters({ id }),
      ).execute();
      return res[0];
    } catch (error: unknown) {
      this.logger.error("Failed to find enrollment by id", error);
      throw error;
    }
  }

  public async findAll(
    criteria?: Partial<Pick<Enrollment, "id" | "academicSessionId">>,
    queryOptions?: ListEnrollmentsQueryDto,
  ): Promise<PaginatedApiResponse<EnrollmentResponse>> {
    try {
      const [items, total] = await Promise.all([
        this.selectEnrollmentQuery(
          this.getFilters({
            ...criteria,
            ...queryOptions,
          }),
        )
          .limit(queryOptions?.limit ?? paginationOptions.LIMIT)
          .offset(queryOptions?.offset ?? paginationOptions.OFFSET),

        this.db.$count(enrollmentTable, and(...this.getFilters(criteria))),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all enrollments", error);
      throw error;
    }
  }

  private selectEnrollmentQuery(filters: SQL[]) {
    const {
      query: selectClassSectionQuery,
      classTeacherStaffAlias,
      classTeacherUserAlias,
    } = getSelectClassSectionSqlQuery();

    return this.db
      .select({
        id: enrollmentTable.id,
        type: enrollmentTable.type,
        status: enrollmentTable.status,
        date: enrollmentTable.date,
        student: sql<EnrollmentResponse["student"]>`json_build_object(
            'id', ${studentTable.id},
            'name', ${studentTable.name}, 
            'rollNumber', ${studentTable.rollNumber},
            'photo', ${studentTable.photo}
          )`,
        classSection: selectClassSectionQuery,
        academicSessionName: academicSessionTable.name,
        createdAt: enrollmentTable.createdAt,
      })
      .from(enrollmentTable)
      .innerJoin(studentTable, eq(studentTable.id, enrollmentTable.studentId))
      .innerJoin(
        classSectionTable,
        eq(classSectionTable.id, enrollmentTable.classSectionId),
      )
      .innerJoin(classTable, eq(classTable.id, classSectionTable.classId))
      .innerJoin(
        academicSessionTable,
        eq(academicSessionTable.id, enrollmentTable.academicSessionId),
      )
      .innerJoin(
        classTeacherStaffAlias,
        eq(classTeacherStaffAlias.id, classSectionTable.classTeacherId),
      )
      .innerJoin(
        classTeacherUserAlias,
        eq(classTeacherUserAlias.id, classTeacherStaffAlias.userId),
      )
      .where(and(...filters));
  }

  private getFilters(
    criteria?: Partial<Pick<Enrollment, "id" | "academicSessionId">> &
      Partial<ListEnrollmentsQueryDto>,
  ) {
    const filters: SQL[] = [];

    if (criteria?.academicSessionId) {
      filters.push(
        eq(enrollmentTable.academicSessionId, criteria.academicSessionId),
      );
    }

    if (criteria?.id) {
      filters.push(eq(enrollmentTable.id, criteria.id));
    }

    if (criteria?.studentId) {
      filters.push(eq(enrollmentTable.studentId, criteria.studentId));
    }

    if (criteria?.sectionId) {
      filters.push(eq(enrollmentTable.classSectionId, criteria.sectionId));
    }

    if (criteria?.classId) {
      filters.push(eq(classSectionTable.classId, criteria.classId));
    }

    if (criteria?.type) {
      filters.push(eq(enrollmentTable.type, criteria.type));
    }

    if (criteria?.status) {
      filters.push(eq(enrollmentTable.status, criteria.status));
    }

    if (criteria?.academicSessionId) {
      filters.push(
        eq(enrollmentTable.academicSessionId, criteria.academicSessionId),
      );
    }

    return filters;
  }
}
