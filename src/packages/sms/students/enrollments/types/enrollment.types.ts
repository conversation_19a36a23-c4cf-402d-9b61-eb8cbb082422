import { enrollmentTable } from "../../../../../db/schemas/index.js";
import { z } from "zod";
import { enrollmentResponseSchema } from "../dto/enrollments.dto.js";

export type Enrollment = typeof enrollmentTable.$inferSelect;
export type NewEnrollment = typeof enrollmentTable.$inferInsert;
export type EnrollmentUpdate = Partial<NewEnrollment>;

export type EnrollmentResponse = z.infer<typeof enrollmentResponseSchema>;
