import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { StudentsService } from "./students.service.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { CreateStudentDto } from "./dto/students.dto.js";
import { AcademicSessionIdParamDto } from "../../../shared/dto/param.dto.js";
import { ListStudentsQueryDto } from "./dto/students-query.dto.js";
import { ApiDocGetStudentsByClassSectionId } from "./docs/students.docs.js";

@Controller()
@ApiTags("Students")
export class StudentsController {
  public constructor(private readonly studentsService: StudentsService) {}

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Post("sms/academic-sessions/:sessionId/students")
  public create(
    @Body() createStudentDto: CreateStudentDto,
    @Param() param: AcademicSessionIdParamDto,
  ) {
    return this.studentsService.create({
      ...createStudentDto,
      academicSessionId: param.sessionId,
    });
  }

  @Roles(["BRANCH_ADMIN", "INSTITUTE_OWNER"])
  @Get("sms/academic-sessions/:sessionId/students")
  public getAllSessionStudents(
    @Param() param: AcademicSessionIdParamDto,
    @Query() query: ListStudentsQueryDto,
  ) {
    return this.studentsService.findAllByAcademicSessionId(
      param.sessionId,
      query,
    );
  }

  @Roles(["BRANCH_ADMIN", "INSTITUTE_OWNER", "TEACHER"])
  @Get("sms/class-sections/:sectionId/students")
  @ApiDocGetStudentsByClassSectionId()
  public getAllSectionStudents(
    @Param("sectionId", ParseUUIDPipe) sectionId: string,
  ) {
    return this.studentsService.findAllByClassSectionId(sectionId);
  }
}
