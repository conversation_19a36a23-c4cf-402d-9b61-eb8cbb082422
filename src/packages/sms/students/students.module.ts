import { Modu<PERSON> } from "@nestjs/common";
import { StudentsController } from "./students.controller.js";
import { StudentsService } from "./students.service.js";
import { GuardiansModule } from "../guardians/guardians.module.js";
import { AcademicSessionsModule } from "../academic-sessions/academic-sessions.module.js";
import { EnrollmentsModule } from "./enrollments/enrollments.module.js";
import { ClassSectionsModule } from "../sections/class-sections.module.js";

@Module({
  imports: [
    GuardiansModule,
    AcademicSessionsModule,
    ClassSectionsModule,
    EnrollmentsModule,
  ],
  controllers: [StudentsController],
  providers: [StudentsService],
  exports: [StudentsService],
})
export class StudentsModule {}
