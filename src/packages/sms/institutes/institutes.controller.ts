import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { InstitutesService } from "./institutes.service.js";
import { createInstituteDto, UpdateInstituteDto } from "./dto/institute.dto.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { ListAllEntitiesQueryDto } from "../../../shared/dto/query.dto.js";
import {
  ApiDocCreateInstitute,
  ApiDocGetOwnerInstitute as ApiDocGetCurrentUserInstitute,
} from "./docs/institute.docs.js";
import { InstituteParamDto } from "./dto/institute-param.dto.js";
import { User } from "../../../shared/decorators/user.decorator.js";
import type { AuthenticatedUser } from "../../../core/auth/type/auth.type.js";

@Controller("sms/institutes")
@ApiTags("Institutes")
export class InstitutesController {
  public constructor(private readonly instituteService: InstitutesService) {}

  @Roles(["PLATFORM_ADMIN"])
  @Get()
  public async getAll(@Query() query: ListAllEntitiesQueryDto) {
    return await this.instituteService.findAll(query);
  }

  @Roles(["INSTITUTE_OWNER"])
  @Get("/me")
  @ApiDocGetCurrentUserInstitute()
  public async getOwnerInstitute(@User() user: AuthenticatedUser) {
    return this.instituteService.findOwnerInstitute(user.id);
  }

  @Roles(["INSTITUTE_OWNER"])
  @Patch("/me")
  public async updateOwnerInstitute(
    @User() user: AuthenticatedUser,
    @Body() updateInstituteDto: UpdateInstituteDto,
  ) {
    return this.instituteService.updateOwnerInstitute(
      user.id,
      updateInstituteDto,
    );
  }

  @Roles(["INSTITUTE_OWNER"])
  @Patch("/:instituteId")
  public async update(
    @Body() updateInstituteDto: UpdateInstituteDto,
    @Param() param: InstituteParamDto,
  ) {
    return this.instituteService.update(param.instituteId, updateInstituteDto);
  }

  @Roles(["INSTITUTE_OWNER"])
  @Post()
  @ApiDocCreateInstitute()
  public async create(
    @Body() createInstituteDto: createInstituteDto,
    @User() user: AuthenticatedUser,
  ) {
    return this.instituteService.create(createInstituteDto, user.id);
  }
}
