import { studentAttendanceTable } from "../../../../../db/schemas/index.js";
import { z } from "zod";
import { studentAttendanceResponseSchema } from "../dto/student-attendance-response.dto.js";

export type NewStudentAttendance = typeof studentAttendanceTable.$inferInsert;
export type StudentAttendance = typeof studentAttendanceTable.$inferSelect;

export type StudentAttendanceResponse = z.infer<
  typeof studentAttendanceResponseSchema
>;
