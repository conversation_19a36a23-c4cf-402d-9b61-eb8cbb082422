import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import {
  getUuidSchema,
  pgDateSchema,
  pgTimestampSchema,
} from "../../../../../shared/schema/zod-common.schema.js";

export const attendanceStatusSchema = z.enum(
  ["PRESENT", "ABSENT", "LATE", "LEAVE"],
  {
    message:
      "Attendance status must be one of 'PRESENT', 'ABSENT', 'LATE', or 'LEAVE'.",
  },
);

export const attendanceBaseSchema = z.object({
  enrollmentId: getUuidSchema("Enrollment ID"),
  date: pgDateSchema.optional(),
  status: attendanceStatusSchema,
  remarks: z.string().nullable().optional(),
});

// ------------------- Create-Student-Attendance-Dto ------------------->
export const createAttendanceSchema = z.array(attendanceBaseSchema);
export class CreateMultipleStudentAttendanceDto extends createZodDto(
  createAttendanceSchema,
) {}

// ------------------- Create-Single-Student-Attendance-Dto ------------------->
export class CreateSingleStudentAttendanceDto extends createZodDto(
  attendanceBaseSchema.omit({ date: true }),
) {}
