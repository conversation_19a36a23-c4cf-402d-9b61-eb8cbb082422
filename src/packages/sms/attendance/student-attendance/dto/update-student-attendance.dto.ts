import { attendanceBaseSchema } from "./create-student-attendance.dto.js";
import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import {
  getUuidSchema,
  pgTimestampSchema,
} from "../../../../../shared/schema/zod-common.schema.js";

export class UpdateBulkStudentAttendanceDto extends createZodDto(
  z.array(
    attendanceBaseSchema.pick({ remarks: true, status: true }).extend({
      id: getUuidSchema("Attendance ID"),
    }),
  ),
) {}

export class CheckoutStudentAttendanceDto extends createZodDto(
  attendanceBaseSchema.pick({ enrollmentId: true }),
) {}

export const updateSingleStudentAttendanceSchema = attendanceBaseSchema
  .pick({
    remarks: true,
    status: true,
  })
  .extend({
    checkedOutTime: pgTimestampSchema.nullable().optional(),
    checkedInTime: pgTimestampSchema.nullable().optional(),
  })
  .refine(
    data => {
      return (
        !data.checkedInTime ||
        !data.checkedOutTime ||
        data.checkedInTime < data.checkedOutTime
      );
    },
    {
      message: "Checked in time must be before checked out time",
    },
  )
  .refine(
    data => {
      if (data.status === "ABSENT" || data.status === "LEAVE") {
        return !data.checkedInTime && !data.checkedOutTime;
      }
      return true;
    },
    {
      message: "Checked in and out time cannot be provided for absent or leave",
    },
  )
  .refine(
    data => {
      if (data.status === "PRESENT" || data.status === "LATE") {
        return data.checkedInTime;
      }
      return true;
    },
    {
      message: "Checked in time is required for present and late status",
    },
  );

export class UpdateSingleStudentAttendanceDto extends createZodDto(
  attendanceBaseSchema
    .pick({
      remarks: true,
      status: true,
    })
    .extend({
      checkedOutTime: pgTimestampSchema.nullable().optional(),
      checkedInTime: pgTimestampSchema.nullable().optional(),
    }),
) {}
