import { createZodDto } from "nestjs-zod";
import { getListEntitiesByDateQuerySchema } from "../../../../../shared/schema/zod-common.schema.js";
import { z } from "zod";
import { attendanceStatusSchema } from "./create-student-attendance.dto.js";

export class ListStudentAttendancesQueryDto extends createZodDto(
  getListEntitiesByDateQuerySchema(
    z.object({ status: attendanceStatusSchema.optional() }), 
  ),
) {}
