import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import {
  getUuidSchema,
  getListEntitiesByDateQuerySchema,
} from "../../../../../shared/schema/zod-common.schema.js";

// ------------------- List-Student-Attendance-Query-Dto ------------------->
export const listStudentAttendanceQuerySchema =
  getListEntitiesByDateQuerySchema(
    z.object({
      studentId: getUuidSchema("Student ID")
        .optional()
        .describe("Filter by student ID"),
    }),
  );

export class ListStudentAttendanceQueryDto extends createZodDto(
  listStudentAttendanceQuerySchema,
) {}
