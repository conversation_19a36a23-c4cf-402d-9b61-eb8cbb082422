import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
  Patch,
  Query,
} from "@nestjs/common";
import { StudentAttendanceService } from "./student-attendance.service.js";
import {
  CreateMultipleStudentAttendanceDto,
  CreateSingleStudentAttendanceDto,
} from "./dto/create-student-attendance.dto.js";
import { ApiTags } from "@nestjs/swagger";
import { Roles } from "../../../../core/roles/decorators/roles.decorator.js";
import {
  CheckoutStudentAttendanceDto,
  UpdateBulkStudentAttendanceDto,
  UpdateSingleStudentAttendanceDto,
} from "./dto/update-student-attendance.dto.js";
import {
  ApiDocCreateManyStudentAttendance,
  ApiDocCreateSingleStudentAttendance,
  ApiDocListStudentAttendance,
  ApiDocUpdateStudentAttendance,
} from "./docs/student-attendance.docs.js";
import { User } from "../../../../shared/decorators/user.decorator.js";
import type { AuthenticatedUser } from "../../../../core/auth/type/auth.type.js";
import { ZodSerializerDto } from "nestjs-zod";
import { StudentAttendanceResponseDto } from "./dto/student-attendance-response.dto.js";
import { getCurrentDateInPgFormat } from "../../../../utils/pg-utils.js";
import { ListStudentAttendancesQueryDto } from "./dto/student-attendance-query.dto.js";

@Controller()
@ApiTags("Student Attendances")
export class StudentAttendanceController {
  constructor(
    private readonly studentAttendanceService: StudentAttendanceService,
  ) {}

  @Post("sms/class-sections/:sectionId/student-attendances")
  @Roles(["TEACHER", "BRANCH_ADMIN"])
  @HttpCode(HttpStatus.CREATED)
  @ApiDocCreateManyStudentAttendance()
  public async create(
    @Param("sectionId", ParseUUIDPipe) classSectionId: string,
    @Body() createStudentAttendanceDto: CreateMultipleStudentAttendanceDto,
    @User() user: AuthenticatedUser,
  ) {
    await this.studentAttendanceService.createInBulk(
      createStudentAttendanceDto.map(item => ({
        ...item,
        date: item.date ?? getCurrentDateInPgFormat(),
        marketedById: user.id,
      })),
      classSectionId,
      { id: user.id, role: user.role },
    );
  }

  @Patch("sms/class-sections/:sectionId/student-attendances")
  @Roles(["TEACHER", "BRANCH_ADMIN"])
  @ApiDocUpdateStudentAttendance()
  public async bulkUpdate(
    @Body() updateStudentAttendanceDto: UpdateBulkStudentAttendanceDto,
    @Param("sectionId", ParseUUIDPipe) classSectionId: string,
    @User() user: AuthenticatedUser,
  ) {
    await this.studentAttendanceService.bulkUpdate(
      updateStudentAttendanceDto,
      classSectionId,
      { id: user.id, role: user.role },
    );
  }

  @Get("sms/class-sections/:sectionId/student-attendances")
  @Roles(["TEACHER", "BRANCH_ADMIN"])
  @ApiDocListStudentAttendance()
  public findAll(
    @Param("sectionId", ParseUUIDPipe) classSectionId: string,
    @Query() query: ListStudentAttendancesQueryDto,
  ) {
    return this.studentAttendanceService.findAll({ classSectionId }, query);
  }

  @Post("sms/student-attendances")
  @Roles(["BRANCH_ADMIN"])
  @ApiDocCreateSingleStudentAttendance()
  @ZodSerializerDto(StudentAttendanceResponseDto)
  public async createSingle(
    @Body() createStudentAttendanceDto: CreateSingleStudentAttendanceDto,
    @User() user: AuthenticatedUser,
  ) {
    return this.studentAttendanceService.createSingle({
      ...createStudentAttendanceDto,
      date: getCurrentDateInPgFormat(),
      marketedById: user.id,
    });
  }

  @Patch("sms/student-attendances/:id")
  @Roles(["TEACHER", "BRANCH_ADMIN"])
  @ApiDocUpdateStudentAttendance()
  public async updateSingle(
    @Param("id", ParseUUIDPipe) id: string,
    @Body() updateStudentAttendanceDto: UpdateSingleStudentAttendanceDto,
    @User() user: AuthenticatedUser,
  ) {
    return this.studentAttendanceService.updateSingle(
      id,
      updateStudentAttendanceDto,
      { id: user.id, role: user.role },
    );
  }

  @Post("sms/student-attendances/check-out")
  @Roles(["BRANCH_ADMIN"])
  @ApiDocUpdateStudentAttendance()
  public async checkout(
    @Body() checkoutStudentAttendanceDto: CheckoutStudentAttendanceDto,
  ) {
    return this.studentAttendanceService.checkout(
      checkoutStudentAttendanceDto.enrollmentId,
    );
  }
}
