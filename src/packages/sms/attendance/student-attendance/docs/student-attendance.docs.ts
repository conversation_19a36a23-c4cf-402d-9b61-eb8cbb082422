import { ApiOperation, ApiResponse } from "@nestjs/swagger";
import {
  useCommonCreateResourceApiResponses,
  useCommonListResourceDocs,
} from "../../../../../docs/shared/api-responses.js";
import { StudentAttendanceResponseDto } from "../dto/student-attendance-response.dto.js";

export function ApiDocCreateManyStudentAttendance() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Create Student Attendance",
      description: "Creates a new student attendance record",
    }),
    ApiResponse({
      status: 201,
      description: "Returns the newly created student attendance record",
      schema: {
        type: "object",
        properties: {
          statusCode: { type: "number", example: 201 },
          message: {
            type: "string",
            example: "Student attendance created successfully",
          },
          data: {
            type: "object",
            example: null,
            nullable: true,
          },
        },
      },
    }),
  );
}

export function ApiDocCreateSingleStudentAttendance() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Create Student Attendance",
      description:
        "Creates a new student attendance record. Only for branch admins.",
    }),
    ApiResponse({
      status: 201,
      description: "Returns the newly created student attendance record",
      type:  StudentAttendanceResponseDto,
    }),
  );
}

export function ApiDocListStudentAttendance() {
  return useCommonListResourceDocs(
    ApiOperation({
      summary: "List Student Attendance",
      description: "Lists all student attendance records",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the list of student attendance records",
      type: StudentAttendanceResponseDto,
      isArray: true,
    }),
  );
}

export function ApiDocUpdateStudentAttendance() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Update Student Attendance",
      description: "Updates a student attendance record",
    }),
    ApiResponse({
      status: 200,
      description: "Returns the updated student attendance record",
      type: StudentAttendanceResponseDto,
      isArray: true,
    }),
  );
}
