import { Module } from "@nestjs/common";
import { StudentAttendanceService } from "./student-attendance.service.js";
import { StudentAttendanceController } from "./student-attendance.controller.js";
import { StaffModule } from "../../staff/staff.module.js";
import { EnrollmentsModule } from "../../students/enrollments/enrollments.module.js";

@Module({
  imports: [StaffModule, EnrollmentsModule],
  controllers: [StudentAttendanceController],
  providers: [StudentAttendanceService],
})
export class StudentAttendanceModule {}
