import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  HttpException,
  Injectable,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import type { Database } from "../../../../db/type.js";
import { InjectDrizzle } from "../../../../shared/modules/drizzle/drizzle.decorators.js";
import {
  handleDatabaseInsertOrUpdateException,
  executeQueryTakeFirstOrThrow,
  getCurrentDateInPgFormat,
} from "../../../../utils/pg-utils.js";
import {
  classSectionTable,
  classTable,
  enrollmentTable,
  Role,
  studentAttendanceTable,
  studentTable,
} from "../../../../db/schemas/index.js";
import {
  NewStudentAttendance,
  StudentAttendanceResponse,
} from "./types/student-attendance.type.js";
import { and, eq, gte, inArray, lte, sql, SQL } from "drizzle-orm";
import { ROLES } from "../../../../core/roles/constants/roles.constants.js";
import { StaffService } from "../../staff/staff.service.js";
import {
  UpdateBulkStudentAttendanceDto,
  UpdateSingleStudentAttendanceDto,
} from "./dto/update-student-attendance.dto.js";
import { ListStudentAttendancesQueryDto } from "./dto/student-attendance-query.dto.js";
import { paginationOptions } from "../../../../shared/constants/api-request.constants.js";
import { EnrollmentService } from "../../students/enrollments/enrollments.service.js";
import {
  PaginatedApiResponse,
  ServiceOptions,
} from "../../../../shared/types/shared.types.js";

@Injectable()
export class StudentAttendanceService {
  private readonly logger = new Logger(StudentAttendanceService.name);

  public constructor(
    @InjectDrizzle() private readonly db: Database,
    private readonly staffService: StaffService,
    private readonly enrollmentsService: EnrollmentService,
  ) {}

  public async createInBulk(
    newStudentAttendanceData: NewStudentAttendance[],
    classSectionId: string,
    marker: {
      id: string;
      role: Role["code"];
    },
  ) {
    try {
      const isTeacher = marker.role === ROLES.TEACHER.code;
      if (isTeacher) {
        await this.verifyTeacherHasGivenAccess(marker.id, classSectionId);
      }

      await this.db
        .insert(studentAttendanceTable)
        .values(newStudentAttendanceData)
        .returning({ id: studentAttendanceTable.id });
    } catch (error: unknown) {
      this.logger.error("Failed to create student attendance", error);
      handleDatabaseInsertOrUpdateException(error, {
        resource: "studentAttendance",
        logger: this.logger,
      });
    }
  }

  public async createSingle(
    newStudentAttendanceData: NewStudentAttendance,
  ): Promise<StudentAttendanceResponse> {
    try {
      return await this.db.transaction(async trx => {
        const { id, date } = await executeQueryTakeFirstOrThrow(
          trx
            .insert(studentAttendanceTable)
            .values({
              ...newStudentAttendanceData,
              checkedInTime: new Date(),
            })
            .returning({
              id: studentAttendanceTable.id,
              date: studentAttendanceTable.date,
            }),
        );

        return await executeQueryTakeFirstOrThrow(
          this.selectAttendanceQuery(this.createFilters({ id, date }), {
            pgTrx: trx,
          }).execute(),
        );
      });
    } catch (error: unknown) {
      this.logger.error("Failed to create student attendance", error);
      handleDatabaseInsertOrUpdateException(error, {
        resource: "studentAttendance",
        logger: this.logger,
        messages: {
          uniqueConstraintError: "Attendance already marked for this student",
        },
      });
    }
  }

  public async checkout(enrollmentId: string) {
    try {
      await this.enrollmentsService.findFirstOrThrow({ id: enrollmentId });

      const existingAttendance = await this.findFirst({
        enrollmentId,
        date: getCurrentDateInPgFormat(),
      });

      if (!existingAttendance) {
        throw new NotFoundException("Attendance for today not found");
      }

      if (!existingAttendance.checkedInTime) {
        throw new BadRequestException("Student has not checked in");
      }

      if (existingAttendance.checkedOutTime) {
        throw new ConflictException("Student already checked out");
      }

      return await this.db.transaction(async trx => {
        const updatedAttendance = await executeQueryTakeFirstOrThrow(
          trx
            .update(studentAttendanceTable)
            .set({
              checkedOutTime: new Date(),
            })
            .where(eq(studentAttendanceTable.id, existingAttendance.id))
            .returning({ id: studentAttendanceTable.id }),
        );

        return executeQueryTakeFirstOrThrow(
          this.selectAttendanceQuery(
            this.createFilters({ id: updatedAttendance.id }),
            {
              pgTrx: trx,
            },
          ).execute(),
        );
      });
    } catch (error: unknown) {
      this.logger.error("Failed to update student attendance", error);
      handleDatabaseInsertOrUpdateException(error, {
        resource: "studentAttendance",
        logger: this.logger,
      });
    }
  }

  public async bulkUpdate(
    updateStudentAttendancesData: UpdateBulkStudentAttendanceDto,
    classSectionId: string,
    marker: {
      id: string;
      role: Role["code"];
    },
  ) {
    try {
      const isTeacher = marker.role === ROLES.TEACHER.code;
      if (isTeacher) {
        await this.verifyTeacherHasGivenAccess(marker.id, classSectionId);
      }

      await this.db.transaction(async trx => {
        for (const {
          id,
          ...studentAttendanceData
        } of updateStudentAttendancesData) {
          await executeQueryTakeFirstOrThrow(
            trx
              .update(studentAttendanceTable)
              .set(studentAttendanceData)
              .where(eq(studentAttendanceTable.id, id))
              .returning({ id: studentAttendanceTable.id }),
          );
        }
      });
    } catch (error: unknown) {
      this.logger.error("Failed to update student attendance", error);
      throw error;
    }
  }

  public async updateSingle(
    attendanceId: string,
    updateStudentAttendanceData: UpdateSingleStudentAttendanceDto,
    marker: {
      id: string;
      role: Role["code"];
    },
  ) {
    try {
      const existingAttendance = await this.findOneByIdOrThrow(attendanceId);
      const isTeacher = marker.role === ROLES.TEACHER.code;
      if (isTeacher) {
        await this.verifyTeacherHasGivenAccess(
          marker.id,
          existingAttendance.classSection.id,
        );
      }

      // make sure to remove checkIn and checkout time for absent or leave
      if (
        updateStudentAttendanceData.status === "ABSENT" ||
        updateStudentAttendanceData.status == "LEAVE"
      ) {
        updateStudentAttendanceData.checkedInTime = null;
        updateStudentAttendanceData.checkedOutTime = null;
      }

      this.verifySingleAttendancePayload(
        updateStudentAttendanceData,
        existingAttendance,
      );

      await executeQueryTakeFirstOrThrow(
        this.db
          .update(studentAttendanceTable)
          .set(updateStudentAttendanceData)
          .where(eq(studentAttendanceTable.id, attendanceId))
          .returning({ id: studentAttendanceTable.id }),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to update student attendance", error);
      throw error;
    }
  }

  public async findOneByIdOrThrow(id: string, options?: ServiceOptions) {
    let existingAttendance: StudentAttendanceResponse | undefined;
    try {
      existingAttendance = await this.findOneById(id, options);
    } catch (error: unknown) {
      this.logger.error("Failed to find student attendance by id", error);
      throw error;
    }
    if (!existingAttendance) {
      throw new NotFoundException(
        `Student attendance with id: ${id} not found`,
      );
    }
    return existingAttendance;
  }

  public async findAll(
    criteria: { id?: string; ids?: string[]; classSectionId?: string } = {},
    queryOptions?: ListStudentAttendancesQueryDto,
    serviceOptions?: ServiceOptions,
  ): Promise<PaginatedApiResponse<StudentAttendanceResponse>> {
    const filterOptions = this.createFilters({
      ...criteria,
      ...(queryOptions ?? {}),
    });

    try {
      const [items, total] = await Promise.all([
        this.selectAttendanceQuery(filterOptions, serviceOptions)
          .limit(queryOptions?.limit ?? paginationOptions.LIMIT)
          .offset(queryOptions?.offset ?? paginationOptions.OFFSET)
          .execute(),

        this.db
          .select({ count: sql<number>`count(*)`.as("count") })
          .from(studentAttendanceTable)
          .innerJoin(
            enrollmentTable,
            eq(enrollmentTable.id, studentAttendanceTable.enrollmentId),
          )
          .innerJoin(
            studentTable,
            eq(studentTable.id, enrollmentTable.studentId),
          )
          .innerJoin(
            classSectionTable,
            eq(classSectionTable.id, enrollmentTable.classSectionId),
          )
          .innerJoin(classTable, eq(classTable.id, classSectionTable.classId))
          .where(and(...filterOptions))
          .then(rows => rows[0]?.count ?? 0),
      ]);

      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all student attendances", error);
      throw error;
    }
  }

  public async findOneById(id: string, options?: ServiceOptions) {
    try {
      const attendances = await this.selectAttendanceQuery(
        this.createFilters({ id }),
        options,
      ).execute();
      return attendances[0];
    } catch (error: unknown) {
      this.logger.error("Failed to find student attendance by id", error);
      throw error;
    }
  }

  public async findFirst(
    criteria: {
      id?: string;
      ids?: string[];
      classSectionId?: string;
      enrollmentId?: string;
      date?: string;
    } = {},
    options?: ServiceOptions,
  ) {
    try {
      const attendances = await this.selectAttendanceQuery(
        this.createFilters(criteria),
        options,
      ).execute();
      return attendances[0];
    } catch (error: unknown) {
      this.logger.error("Failed to find student attendance by id", error);
      throw error;
    }
  }

  private verifySingleAttendancePayload(
    data: UpdateSingleStudentAttendanceDto,
    existingAttendance: StudentAttendanceResponse,
  ) {
    if (data.checkedOutTime && !data.checkedInTime) {
      throw new BadRequestException("Checked in time is required for checkout");
    }

    if (data.checkedOutTime && existingAttendance.checkedOutTime) {
      throw new ConflictException("Student already checked out");
    }
  }

  private selectAttendanceQuery(filters: SQL[], options?: ServiceOptions) {
    const dbClient = options?.pgTrx ?? this.db;
    return dbClient
      .select({
        id: studentAttendanceTable.id,
        status: studentAttendanceTable.status,
        remarks: studentAttendanceTable.remarks,
        date: studentAttendanceTable.date,
        createdAt: studentAttendanceTable.createdAt,
        checkedInTime: studentAttendanceTable.checkedInTime,
        checkedOutTime: studentAttendanceTable.checkedOutTime,
        student: sql<StudentAttendanceResponse["student"]>`json_build_object(
            'id', ${studentTable.id},
            'name', ${studentTable.name},
            'rollNumber', ${studentTable.rollNumber},
            'enrollmentId', ${enrollmentTable.id}
          )`,
        classSection: sql<
          StudentAttendanceResponse["classSection"]
        >`json_build_object(
            'id', ${classSectionTable.id},
            'name', ${classSectionTable.name},
            'totalStudents', (
                SELECT COUNT(*)::int
                  FROM ${enrollmentTable}
                  WHERE ${enrollmentTable.classSectionId} = ${classSectionTable.id}
              ),
              'class', json_build_object(
                'id', ${classTable.id},
                'name', ${classTable.name}
              )
            )`,
      })
      .from(studentAttendanceTable)
      .innerJoin(
        enrollmentTable,
        eq(enrollmentTable.id, studentAttendanceTable.enrollmentId),
      )
      .innerJoin(studentTable, eq(studentTable.id, enrollmentTable.studentId))
      .innerJoin(
        classSectionTable,
        eq(classSectionTable.id, enrollmentTable.classSectionId),
      )
      .innerJoin(classTable, eq(classTable.id, classSectionTable.classId))
      .where(and(...filters));
  }

  private createFilters(
    options: {
      id?: string;
      ids?: string[];
      classSectionId?: string;
      enrollmentId?: string;
    } & Partial<ListStudentAttendancesQueryDto>,
  ) {
    const filters: SQL[] = [];

    if (options.classSectionId) {
      filters.push(eq(enrollmentTable.classSectionId, options.classSectionId));
    }

    if (options.ids) {
      filters.push(inArray(studentAttendanceTable.id, options.ids));
    }

    if (options.id) {
      filters.push(eq(studentAttendanceTable.id, options.id));
    }

    if (options.enrollmentId) {
      filters.push(
        eq(studentAttendanceTable.enrollmentId, options.enrollmentId),
      );
    }

    if (options.date) {
      filters.push(eq(studentAttendanceTable.date, options.date));
    }

    if (options.status) {
      filters.push(eq(studentAttendanceTable.status, options.status));
    }

    if (options.month) {
      const [year, month] = options.month.split("-");
      if (year && month) {
        filters.push(
          eq(
            sql`EXTRACT(YEAR FROM ${studentAttendanceTable.date})`,
            parseInt(year),
          ),
          eq(
            sql`EXTRACT(MONTH FROM ${studentAttendanceTable.date})`,
            parseInt(month),
          ),
        );
      }
    }

    if (options.from && options.to) {
      filters.push(
        gte(sql`DATE(${studentAttendanceTable.date})`, options.from),
        lte(sql`DATE(${studentAttendanceTable.date})`, options.to),
      );
    }

    if (!options.date) {
      filters.push(eq(studentAttendanceTable.date, getCurrentDateInPgFormat()));
    }

    return filters;
  }

  private async verifyTeacherHasGivenAccess(
    teacherId: string,
    classSectionId: string,
  ) {
    try {
      const isClassTeacher = await this.staffService.checkTeacherIsClassTeacher(
        classSectionId,
        teacherId,
      );
      if (isClassTeacher) {
        return;
      }

      throw new ForbiddenException("You do not have access to this resource");
    } catch (error) {
      if (!(error instanceof HttpException)) {
        this.logger.error("Failed to verify teacher has access", error);
      }
      throw error;
    }
  }
}
