import { ApiOperation, ApiResponse } from "@nestjs/swagger";
import { useCommonCreateResourceApiResponses } from "../../../../docs/shared/api-responses.js";
import { SectionSubjectAssignmentResponseDto } from "../dto/section-subjects.dto.js";

export function ApiDocAssignSubjectToSection() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Assign Subject to Section",
      description: "Assigns a subject to a specific class section.",
    }),
    ApiResponse({
      status: 200,
      type: SectionSubjectAssignmentResponseDto,
      description: "Successfully assigned subject to section",
    }),
  );
}
