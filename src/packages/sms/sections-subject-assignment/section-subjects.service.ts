import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import {
  EntityId,
  ListAllEntitiesQueryOptions,
} from "../../../shared/types/shared.types.js";
import {
  NewSectionSubject,
  SectionSubject,
  SectionSubjectAssignmentResponse,
} from "./types/section-subjects.type.js";
import {
  handleDatabaseInsertException,
  executeQueryTakeFirstOrThrow,
} from "../../../utils/pg-utils.js";
import { Class } from "../classes/types/classes.types.js";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../db/type.js";
import {
  classSectionTable,
  classTable,
  sectionSubjectTable,
  staffTable,
  subjectTable,
  usersTable,
} from "../../../db/schemas/index.js";
import { and, desc, eq, SQL, sql } from "drizzle-orm";

@Injectable()
export class SectionSubjectsService {
  private readonly logger = new Logger(SectionSubjectsService.name);

  constructor(@InjectDrizzle() private readonly db: Database) {}

  public async assignSubjectToSection(
    assignSubjectPayload: NewSectionSubject,
  ): Promise<EntityId> {
    try {
      return await executeQueryTakeFirstOrThrow(
        this.db
          .insert(sectionSubjectTable)
          .values(assignSubjectPayload)
          .returning({ id: sectionSubjectTable.id }),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to assign subject to section", error);
      handleDatabaseInsertException(error, {
        resource: "sectionSubject",
        logger: this.logger,
        messages: {
          uniqueConstraintError: "Subject already assigned to section",
        },
      });
    }
  }

  public async updateSubjectAssignment(
    id: SectionSubject["id"],
    updateSectionSubjectPayload: Partial<NewSectionSubject>,
  ) {
    try {
      return await executeQueryTakeFirstOrThrow(
        this.db
          .update(sectionSubjectTable)
          .set(updateSectionSubjectPayload)
          .where(eq(sectionSubjectTable.id, id))
          .returning({ id: sectionSubjectTable.id }),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to update subject assignment", error);
      throw error;
    }
  }

  public async find(
    criteria: Partial<SectionSubject> &
      Partial<Pick<Class, "academicSessionId">>,
    queryOptions?: ListAllEntitiesQueryOptions,
  ) {
    const filters: SQL[] = [];

    if (criteria.academicSessionId) {
      filters.push(
        eq(classTable.academicSessionId, criteria.academicSessionId),
      );
    }

    if (criteria.id) {
      filters.push(eq(sectionSubjectTable.id, criteria.id));
    }

    if (criteria.classSectionId) {
      filters.push(
        eq(sectionSubjectTable.classSectionId, criteria.classSectionId),
      );
    }

    if (criteria.subjectId) {
      filters.push(eq(sectionSubjectTable.subjectId, criteria.subjectId));
    }

    if (criteria.subjectTeacherId) {
      filters.push(
        eq(sectionSubjectTable.subjectTeacherId, criteria.subjectTeacherId),
      );
    }

    try {
      const query = this.db
        .select({
          id: sectionSubjectTable.id,
          academicSessionId: sectionSubjectTable.academicSessionId,
          class: sql<
            SectionSubjectAssignmentResponse["class"]
          >`json_build_object(
            'id', ${classTable.id},
            'name', ${classTable.name}
          )`,
          section: sql<
            SectionSubjectAssignmentResponse["section"]
          >`json_build_object(
            'id', ${classSectionTable.id},
            'name', ${classSectionTable.name},
            'totalStudents', (
              SELECT COUNT(*)
              FROM enrollment
              WHERE enrollment.class_section_id = ${classSectionTable.id}
            )
          )`,
          subject: sql<
            SectionSubjectAssignmentResponse["subject"]
          >`json_build_object(
            'id', ${subjectTable.id},
            'name', ${subjectTable.name}
          )`,
          subjectTeacher: sql<
            SectionSubjectAssignmentResponse["subjectTeacher"]
          >`json_build_object(
            'id', ${usersTable.id},
            'name', ${usersTable.name}
          )`,
          createdAt: sectionSubjectTable.createdAt,
        })
        .from(sectionSubjectTable)
        .innerJoin(
          classSectionTable,
          eq(classSectionTable.id, sectionSubjectTable.classSectionId),
        )

        .innerJoin(
          subjectTable,
          eq(subjectTable.id, sectionSubjectTable.subjectId),
        )
        .innerJoin(classTable, eq(classTable.id, classSectionTable.classId))
        .innerJoin(
          staffTable,
          eq(staffTable.id, sectionSubjectTable.subjectTeacherId),
        )
        .innerJoin(usersTable, eq(usersTable.id, staffTable.userId))
        .where(and(...filters))
        .orderBy(desc(sectionSubjectTable.createdAt));
      if (queryOptions?.limit) {
        query.limit(queryOptions.limit);
      }
      if (queryOptions?.offset) {
        query.offset(queryOptions.offset);
      }

      return await query.execute();
    } catch (error: unknown) {
      this.logger.error("Failed to find section subjects", error);
      throw error;
    }
  }

  public async findAllByAcademicSessionId(
    academicSessionId: SectionSubject["academicSessionId"],
    queryOptions: ListAllEntitiesQueryOptions,
  ) {
    try {
      const [items, total] = await Promise.all([
        this.find({ academicSessionId }, queryOptions),

        this.db.$count(
          sectionSubjectTable,
          eq(sectionSubjectTable.academicSessionId, academicSessionId),
        ),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error(
        "Failed to find all subjects by class section id",
        error,
      );
      throw error;
    }
  }

  public async findById(
    id: SectionSubject["id"],
  ): Promise<SectionSubjectAssignmentResponse | undefined> {
    try {
      const res = await this.find({ id });
      return res[0];
    } catch (error: unknown) {
      this.logger.error("Failed to find section subject by id", error);
      throw error;
    }
  }

  public async findByIdOrThrow(id: SectionSubject["id"]) {
    let existingSectionSubject: SectionSubjectAssignmentResponse | undefined;
    try {
      existingSectionSubject = await this.findById(id);
    } catch (error: unknown) {
      this.logger.error(
        "Failed to find section subject assignment by id",
        error,
      );
      throw error;
    }
    if (!existingSectionSubject) {
      throw new NotFoundException(
        `Section subject assignment with id: ${id} not found`,
      );
    }
    return existingSectionSubject;
  }

  public async verifySectionSubjectAssignmentExists(id: SectionSubject["id"]) {
    let existingSectionSubject: SectionSubjectAssignmentResponse | undefined;
    try {
      existingSectionSubject = await this.findById(id);
    } catch (error: unknown) {
      this.logger.error(
        "Failed to verify section subject assignment exists",
        error,
      );
      throw error;
    }

    if (!existingSectionSubject) {
      throw new NotFoundException(
        `Section subject assignment with id: ${id} not found`,
      );
    }
  }
}
