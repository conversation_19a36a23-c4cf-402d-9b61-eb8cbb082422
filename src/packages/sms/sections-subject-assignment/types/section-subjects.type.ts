import { Insertable } from "kysely";
import { SectionSubjectTable } from "../../../../database/types.js";
import { sectionSubjectAssignmentResponseSchema } from "../dto/section-subjects.dto.js";
import { z } from "zod";
import { sectionSubjectTable } from "../../../../db/schemas/index.js";

export type SectionSubject = typeof sectionSubjectTable.$inferSelect;
export type NewSectionSubject = typeof sectionSubjectTable.$inferInsert;

export type AssignSubjectToSectionPayload = Insertable<SectionSubjectTable>;

export type SectionSubjectAssignmentResponse = z.infer<
  typeof sectionSubjectAssignmentResponseSchema
>;
