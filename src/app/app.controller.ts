import { Controller, Get } from "@nestjs/common";
import { AppService } from "./app.service.js";
import { ApiPingResponse } from "./swagger/app.swagger.js";
import { ApiTags } from "@nestjs/swagger";
import { Public } from "../shared/decorators/public.decorator.js";
import { AppStatus } from "./types/app.type.js";

@ApiTags("Health Check")
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @ApiPingResponse()
  @Public()
  @Get("/status")
  public getAppStatus(): Promise<AppStatus> {
    return this.appService.getAppStatus();
  }
}
