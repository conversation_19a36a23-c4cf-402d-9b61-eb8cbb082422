import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from "@nestjs/common";

import { AppStatus } from "./types/app.type.js";
import type { Database } from "../db/type.js";
import { sql } from "drizzle-orm";
import { InjectDrizzle } from "../shared/modules/drizzle/drizzle.decorators.js";

@Injectable()
export class AppService {
  private readonly logger = new Logger("AppService");
  public constructor(@InjectDrizzle() private readonly db: Database) {}

  public async getAppStatus(): Promise<AppStatus> {
    try {
      await this.db.execute(sql`SELECT 1`);
      return {
        message: "Server is up and running",
        timeStamp: new Date().toISOString(),
      };
    } catch (error: unknown) {
      this.logger.error(error, "App Service - Check Status");
      throw new InternalServerErrorException("Server is down");
    }
  }
}
