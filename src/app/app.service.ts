import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from "@nestjs/common";
import { Kysely, sql } from "kysely";
import { DB } from "../database/types.js";
import { InjectKysely } from "../shared/modules/kysely/decorators/kysely.decorators.js";
import { AppStatus } from "./types/app.type.js";

@Injectable()
export class AppService {
  private readonly logger = new Logger("AppService");

  public constructor(@InjectKysely() private readonly db: Kysely<DB>) {}

  public async getAppStatus(): Promise<AppStatus> {
    try {
      await sql`SELECT 1`.execute(this.db);
      return {
        message: "Server is up and running",
        timeStamp: new Date().toISOString(),
      };
    } catch (error: unknown) {
      this.logger.error(error, "App Service - Check Status");
      throw new InternalServerErrorException("Server is down");
    }
  }
}
