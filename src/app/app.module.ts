import { Module } from "@nestjs/common";
import { AppService } from "./app.service.js";
import { AppController } from "./app.controller.js";
import { ConfigModule } from "@nestjs/config";
import { AuthModule } from "../core/auth/auth.module.js";
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from "@nestjs/core";
import { CustomZodValidationPipe } from "../lib/zod.js";
import { ZodSerializerInterceptor } from "nestjs-zod";
import { AllExceptionFilter } from "../shared/filters/All-exception.filter.js";
import { envSchema } from "../shared/services/env/env.schema.js";
import { CamelCasePlugin, PostgresDialect } from "kysely";
import pg from "pg";
import { KyselyModule } from "../shared/modules/kysely/kysely.module.js";
import { readFileSync } from "fs";
import { ServeStaticModule } from "@nestjs/serve-static";
import { join } from "path";
import { InstitutesModule } from "../packages/sms/institutes/institutes.module.js";
import { FileUploadModule } from "../shared/modules/file-uploads/fileuploads.module.js";
import { JwtAuthGuard } from "../shared/guards/jwt-auth.guard.js";
import { RolesGuard } from "../core/roles/guards/roles.guard.js";
import { jwtConstants } from "../core/auth/constants/jwt-constants.js";
import { JwtModule } from "@nestjs/jwt";
import { SubscriptionPlansModule } from "../packages/platform/subscriptions-plans/subscription-plans.module.js";
import { InstituteOwnersModule } from "../packages/sms/institute-owners/owners.module.js";
import { BranchesModule } from "../packages/sms/branches/branches.module.js";
import { AcademicSessionsModule } from "../packages/sms/academic-sessions/academic-sessions.module.js";
import { EnvService } from "../shared/services/env/env.service.js";
import { CommonModule } from "../shared/shared.module.js";
import { StaffModule } from "../packages/sms/staff/staff.module.js";
import { ClassesModule } from "../packages/sms/classes/classes.module.js";
import { UsersModule } from "../core/users/users.module.js";
import { StudentsModule } from "../packages/sms/students/students.module.js";
import { SubjectsModule } from "../packages/sms/subjects/subjects.module.js";
import { SectionSubjectsModule } from "../packages/sms/sections-subject-assignment/section-subjects.module.js";
import { DiaryModule } from "../packages/sms/diary/diary.module.js";
import { DrizzleModule } from "../shared/modules/drizzle/drizzle.module.js";
import * as schema from "../db/schemas/index.js";
import { ClassSectionsModule } from "../packages/sms/sections/class-sections.module.js";
import { StudentAttendanceModule } from "../packages/sms/attendance/student-attendance/student-attendance.module.js";
import { ThrottlerGuard, ThrottlerModule } from "@nestjs/throttler";
import { ExamsModule } from "../packages/sms/exams/exams.module.js";
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ".env",
      validate: env => envSchema.parse(env),
    }),
    JwtModule.register({
      global: true,
      secret: jwtConstants.accessToken.secret,
      signOptions: { expiresIn: jwtConstants.accessToken.expireTimeInSec },
    }),
    ServeStaticModule.forRootAsync({
      useFactory: (envService: EnvService) => {
        return [
          {
            rootPath: join(
              import.meta.dirname,
              "..",
              "..",
              envService.get("UPLOADS_DIR"),
            ),
            serveRoot: `${envService.get("API_PREFIX")}/${envService.get("UPLOADS_DIR")}`,
          },
        ];
      },
      inject: [EnvService],
    }),
    KyselyModule.forRootAsync({
      inject: [EnvService],
      useFactory: (envService: EnvService) => ({
        dialect: new PostgresDialect({
          pool: new pg.Pool({
            database: envService.get("DATABASE_NAME"),
            host: envService.get("DATABASE_HOST"),
            port: envService.get("DATABASE_PORT"),
            user: envService.get("DATABASE_USER"),
            password:
              envService.get("DATABASE_PASSWORD") ??
              readFileSync(envService.get("DATABASE_PASSWORD_FILE"), "utf-8"),
          }),
        }),
        plugins: [new CamelCasePlugin()],
      }),
    }),
    DrizzleModule.forRootAsync({
      inject: [EnvService],
      useFactory: (envService: EnvService) => ({
        postgres: {
          url: envService.get("DATABASE_URL"),
        },
        config: { schema: { ...schema }, casing: "snake_case" },
      }),
    }),
    ThrottlerModule.forRoot({
      throttlers: [
        {
          name: "short",
          ttl: 1000,
          limit: 3,
        },
        {
          name: "medium",
          ttl: 10000,
          limit: 20,
        },
        {
          name: "long",
          ttl: 60000,
          limit: 100,
        },
      ],
    }),
    CommonModule,
    UsersModule,
    SubscriptionPlansModule,
    AuthModule,
    FileUploadModule,
    InstituteOwnersModule,
    InstitutesModule,
    BranchesModule,
    AcademicSessionsModule,
    StaffModule,
    ClassesModule,
    StudentsModule,
    ClassSectionsModule,
    SubjectsModule,
    SectionSubjectsModule,
    DiaryModule,
    StudentAttendanceModule,
    ExamsModule,
  ],
  controllers: [AppController],
  exports: [AppService],
  providers: [
    AppService,
    // validates req body using zod schema
    {
      provide: APP_PIPE,
      useClass: CustomZodValidationPipe,
    },
    // intercepts and validates api response using zod schema dto
    {
      provide: APP_INTERCEPTOR,
      useClass: ZodSerializerInterceptor,
    },
    // extended filter to handle zod serialization exceptions and http exceptions
    {
      provide: APP_FILTER,
      useClass: AllExceptionFilter,
    },
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule {}
