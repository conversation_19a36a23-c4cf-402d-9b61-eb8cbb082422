import { Test } from "@nestjs/testing";
import { AppController } from "../app.controller.js";
import { AppService } from "../app.service.js";
import { appServiceMock } from "./mocks/app.service.mock.js";

describe("AppController", () => {
  let appController: AppController;

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      controllers: [AppController],
      providers: [
        {
          provide: AppService,
          useValue: appServiceMock,
        },
      ],
    }).compile();
    appController = moduleRef.get<AppController>(AppController);
  });

  describe("getAppStatus", () => {
    it("should return app status", async () => {
      const status = {
        message: "Server is up and running",
        timeStamp: new Date().toISOString(),
      };
      appServiceMock.getAppStatus.mockResolvedValue(status);
      expect(appController.getAppStatus()).toBeInstanceOf(Promise);
      expect(await appController.getAppStatus()).toEqual(status);
    });
  });
});
