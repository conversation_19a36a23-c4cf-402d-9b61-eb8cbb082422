import { sql } from "drizzle-orm";
import { classSectionTable } from "../db/schemas/education.schema.js";
import { enrollmentTable } from "../db/schemas/student.schema.js";
import {
  classTable,
  examTable,
  staffTable,
  subjectTable,
  usersTable,
} from "../db/schemas/index.js";
import { ClassSectionResponse } from "../packages/sms/sections/types/class-sections.types.js";
import { alias } from "drizzle-orm/pg-core";
import { Exam } from "../packages/sms/exams/types/exams.type.js";

export function getSelectClassSectionSqlQuery() {
  const classTeacherStaffAlias = alias(staffTable, "class_teacher");
  const classTeacherUserAlias = alias(usersTable, "class_teacher_user");

  const query = sql<ClassSectionResponse>`json_build_object(
              'id', ${classSectionTable.id},
              'name', ${classSectionTable.name},
              'isActive', ${classSectionTable.isActive},
              'createdAt', ${classSectionTable.createdAt},
              'totalStudents', (
                SELECT COUNT(*)::int
                FROM ${enrollmentTable}
                WHERE ${enrollmentTable.classSectionId} = ${classSectionTable.id} AND ${enrollmentTable.status} = 'ACTIVE'
              ),
              'class', json_build_object(
                'id', ${classTable.id},
                'name', ${classTable.name}
              ),
              'classTeacher', json_build_object(
                'id', ${classTeacherStaffAlias.id},
                'name', ${classTeacherUserAlias.name}
              )
            )`;

  return {
    query,
    classTeacherStaffAlias,
    classTeacherUserAlias,
  };
}

export const selectExamQuery = sql<
  Omit<Exam, "academicSessionId" | "createdBy">
>`json_build_object(
  'id', ${examTable.id},
  'name', ${examTable.name},
  'startDate', ${examTable.startDate},
  'endDate', ${examTable.endDate},
  'createdAt', ${examTable.createdAt}
)`;

export const getSelectSubjectQuery = () => {
  const subjectTeacherUserAlias = alias(usersTable, "subject_teacher_user");
  const subjectTeacherStaffAlias = alias(staffTable, "subject_teacher_staff");

  const selectSubjectQuery = sql<{
    id: string;
    name: string;
    teacher: { id: string; name: string };
  }>`json_build_object(
                'id', ${subjectTable.id},
                'name', ${subjectTable.name},
                'teacher', json_build_object(
                  'id', ${subjectTeacherUserAlias.id},
                  'name', ${subjectTeacherUserAlias.name}
                )
              )`;

  return {
    query: selectSubjectQuery,
    subjectTeacherUserAlias,
    subjectTeacherStaffAlias,
  };
};
