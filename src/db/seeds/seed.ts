import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as schema from "../schemas/index.js";
import { ROLES } from "../../core/roles/constants/roles.constants.js";
import { Database } from "../type.js";
import { eq } from "drizzle-orm";
import { PLATFORM_ADMIN } from "../../core/users/constants/users.constant.js";
import "dotenv/config";

async function main() {
  const databaseUrl = process.env.DATABASE_URL;
  if (!databaseUrl) {
    throw new Error("DATABASE_URL is not defined");
  }

  const queryClient = postgres(databaseUrl);
  const db = drizzle({
    client: queryClient,
    casing: "snake_case",
    schema: { ...schema },
  });

  await seedRoles(db);
  await seedSuperAdmin(db);
  await db.$client.end();
}

async function seedRoles(db: Database) {
  try {
    await db
      .insert(schema.roleTable)
      .values(Object.values(ROLES))
      .onConflictDoNothing();
  } catch (error: unknown) {
    console.error("Failed to seed roles", error);
    throw error;
  }
}

async function seedSuperAdmin(db: Database) {
  try {
    const superAdminRole = await db.query.roleTable.findFirst({
      where: eq(schema.roleTable.name, "PLATFORM_ADMIN"),
    });

    if (!superAdminRole) {
      throw new Error("Super admin role not found");
    }

    await db
      .insert(schema.usersTable)
      .values({
        ...PLATFORM_ADMIN,
        roleId: superAdminRole.id,
      })
      .returning({ id: schema.usersTable.id })
      .onConflictDoNothing()
      .execute();

    const adminAccount = await db.query.usersTable.findFirst({
      where: eq(schema.usersTable.email, PLATFORM_ADMIN.email),
    });

    if (!adminAccount) {
      throw new Error("Super admin account not found");
    }

    await db
      .insert(schema.platformAdminTable)
      .values({ userId: adminAccount.id })
      .onConflictDoNothing()
      .execute();

    console.log("Super admin seeded");
  } catch (error: unknown) {
    console.error("Failed to seed super admin", error);
    throw error;
  }
}

main()
  .then(() => {
    console.log("Seeding completed");
  })
  .catch((err: unknown) => {
    console.error("Failed to seed database", err);
  });
