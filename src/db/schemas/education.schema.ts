import {
  pgTable,
  uuid,
  text,
  boolean,
  timestamp,
  date,
  unique,
  index,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import {
  classTable,
  subjectTable,
  academicSessionTable,
} from "./academic.schema.js";
import { staffTable } from "./staff.schema.js";

// ==================== TABLES ====================

/**
 * Class sections table
 * Divisions/sections within each class
 */
export const classSectionTable = pgTable(
  "class_section",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    name: text("name").notNull(),
    classId: uuid("class_id")
      .notNull()
      .references(() => classTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    classTeacherId: uuid("class_teacher_id")
      .notNull()
      .references(() => staffTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    isActive: boolean("is_active").notNull().default(true),
    createdAt: timestamp("created_at", { withTimezone: true })
      .notNull()
      .default(sql`now()`),
  },
  table => [
    // Unique constraint for section name within class
    unique("class_section_name_class_id_unique").on(table.name, table.classId),
    // Index for class queries
    index("idx_class_section_class_id").on(table.classId),
  ],
);

/**
 * Section subjects table
 * Assignment of subjects to class sections with teachers
 */
export const sectionSubjectTable = pgTable(
  "section_subject",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    classSectionId: uuid("class_section_id")
      .notNull()
      .references(() => classSectionTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    subjectId: uuid("subject_id")
      .notNull()
      .references(() => subjectTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    academicSessionId: uuid("academic_session_id")
      .notNull()
      .references(() => academicSessionTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    subjectTeacherId: uuid("subject_teacher_id")
      .notNull()
      .references(() => staffTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    createdAt: timestamp("created_at", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
  },
  table => [
    // Unique constraint for subject per class section
    unique("unique_subject_per_class_section").on(
      table.subjectId,
      table.classSectionId,
      table.academicSessionId,
    ),
    // Index for academic session queries
    index("idx_academic_session_id").on(table.academicSessionId),
  ],
);

/**
 * Diary table
 * Daily diary entries for class sections and subjects
 */
export const diaryTable = pgTable(
  "diary",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    classSectionId: uuid("class_section_id")
      .notNull()
      .references(() => classSectionTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    teacherId: uuid("teacher_id")
      .notNull()
      .references(() => staffTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    subjectId: uuid("subject_id")
      .notNull()
      .references(() => subjectTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    content: text("content").notNull(),
    date: date("date").notNull(),
    createdAt: timestamp("created_at", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
  },
  table => [
    // Unique constraint for diary entry per section, subject, and date
    unique("unique_diary_entry").on(
      table.classSectionId,
      table.subjectId,
      table.date,
    ),
  ],
);

// ==================== TYPES ====================

/**
 * Type definitions for the education schema tables
 */
export type ClassSection = typeof classSectionTable.$inferSelect;
export type NewClassSection = typeof classSectionTable.$inferInsert;
