import {
  pgTable,
  uuid,
  text,
  timestamp,
  unique,
  check,
  pgEnum,
  integer,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { usersTable, genderEnum } from "./auth.schema.js";
import { branchTable } from "./institute.schema.js";

// ==================== ENUMS ====================

/**
 * Staff department enumeration
 */
export const staffDepartmentEnum = pgEnum("staff_department", [
  "ACADEMIC",
  "ADMINISTRATION",
  "SUPPORT",
]);

/**
 * Staff type enumeration
 */
export const staffTypeEnum = pgEnum("staff_type", [
  "TEACHER",
  "SUPPORT_STAFF",
  "BRANCH_ADMIN",
  "ACCOUNTANT",
]);

// ==================== TABLES ====================

/**
 * Support staff profile table
 * Profiles for staff members who don't have user accounts
 */
export const supportStaffProfileTable = pgTable("support_staff_profile", {
  id: uuid("id")
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  phone: text("phone").notNull().unique(),
  address: text("address").notNull(),
  gender: genderEnum("gender").notNull(),
  photo: text("photo"),
  cnic: text("cnic").notNull().unique(),
  createdAt: timestamp("created_at", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
});

/**
 * Staff table
 * Links users or support staff profiles to staff positions
 */
export const staffTable = pgTable(
  "staff",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    userId: uuid("user_id")
      .unique()
      .references(() => usersTable.id),
    branchId: uuid("branch_id")
      .notNull()
      .references(() => branchTable.id),
    supportStaffProfileId: uuid("support_staff_profile_id")
      .unique()
      .references(() => supportStaffProfileTable.id),
    designation: text("designation").notNull(),
    department: staffDepartmentEnum("department").notNull(),
    salary: integer("salary").notNull(),
    type: staffTypeEnum("type").notNull(),
    createdAt: timestamp("created_at", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
  },
  table => [
    // Check constraint to ensure staff is either a user or support staff, not both
    check(
      "staff_is_either_user_or_support_staff",
      sql`(${table.userId} IS NOT NULL AND ${table.supportStaffProfileId} IS NULL) OR (${table.userId} IS NULL AND ${table.supportStaffProfileId} IS NOT NULL)`,
    ),
    // Unique index for user per branch
    unique("staff_user_id_unique_index").on(table.userId, table.branchId),
    // Unique index for support staff profile per branch
    unique("staff_support_staff_profile_id_unique_index").on(
      table.supportStaffProfileId,
      table.branchId,
    ),
  ],
);
// ==================== TYPES ====================

/**
 * Type definitions for the staff schema tables
 */
export type SupportStaffProfile = typeof supportStaffProfileTable.$inferSelect;
export type NewSupportStaffProfile =
  typeof supportStaffProfileTable.$inferInsert;

export type Staff = typeof staffTable.$inferSelect;
export type NewStaff = typeof staffTable.$inferInsert;
