import { relations } from "drizzle-orm";

import { studentAttendanceTable } from "./student-attendance.schema.js";
import { roleTable, usersTable } from "./auth.schema.js";
import {
  platformAdminTable,
  subscriptionPlanTable,
  subscriptionTable,
} from "./platform.schema.js";
import {
  branchTable,
  instituteOwnerTable,
  instituteTable,
} from "./institute.schema.js";
import {
  academicSessionTable,
  classTable,
  subjectTable,
} from "./academic.schema.js";
import { staffTable, supportStaffProfileTable } from "./staff.schema.js";
import {
  enrollmentTable,
  guardianTable,
  studentTable,
} from "./student.schema.js";
import {
  classSectionTable,
  diaryTable,
  sectionSubjectTable,
} from "./education.schema.js";
import {
  classSectionExamTable,
  examResultTable,
  examScheduleTable,
  examTable,
} from "./exams.schema.js";
import { studentCardTemplateTable } from "./student-card-template.schema.js";

// ==================== USER RELATIONS ====================

export const usersRelations = relations(usersTable, ({ one }) => ({
  role: one(roleTable, {
    fields: [usersTable.roleId],
    references: [roleTable.id],
  }),
  staff: one(staffTable, {
    fields: [usersTable.id],
    references: [staffTable.userId],
  }),
  guardian: one(guardianTable, {
    fields: [usersTable.id],
    references: [guardianTable.userId],
  }),
  platformAdmin: one(platformAdminTable, {
    fields: [usersTable.id],
    references: [platformAdminTable.userId],
  }),
}));

export const roleRelations = relations(roleTable, ({ many }) => ({
  users: many(usersTable),
}));

// ==================== PLATFORM RELATIONS ====================

export const platformAdminRelations = relations(
  platformAdminTable,
  ({ one }) => ({
    user: one(usersTable, {
      fields: [platformAdminTable.userId],
      references: [usersTable.id],
    }),
  }),
);

export const subscriptionPlanRelations = relations(
  subscriptionPlanTable,
  ({ many }) => ({
    subscriptions: many(subscriptionTable),
  }),
);

export const subscriptionRelations = relations(
  subscriptionTable,
  ({ one }) => ({
    plan: one(subscriptionPlanTable, {
      fields: [subscriptionTable.planId],
      references: [subscriptionPlanTable.id],
    }),
    owner: one(instituteOwnerTable, {
      fields: [subscriptionTable.ownerId],
      references: [instituteOwnerTable.userId],
    }),
  }),
);

// ==================== INSTITUTE RELATIONS ====================

export const instituteOwnerRelations = relations(
  instituteOwnerTable,
  ({ one }) => ({
    user: one(usersTable, {
      fields: [instituteOwnerTable.userId],
      references: [usersTable.id],
    }),
    institute: one(instituteTable),
    subscription: one(subscriptionTable),
  }),
);

export const instituteRelations = relations(
  instituteTable,
  ({ one, many }) => ({
    owner: one(instituteOwnerTable, {
      fields: [instituteTable.ownerId],
      references: [instituteOwnerTable.userId],
    }),
    branches: many(branchTable),
  }),
);

export const branchRelations = relations(branchTable, ({ one, many }) => ({
  institute: one(instituteTable, {
    fields: [branchTable.instituteId],
    references: [instituteTable.id],
  }),
  academicSessions: many(academicSessionTable),
  staff: many(staffTable),
  studentCardTemplate: one(studentCardTemplateTable),
}));

// ==================== ACADEMIC RELATIONS ====================

export const academicSessionRelations = relations(
  academicSessionTable,
  ({ one, many }) => ({
    branch: one(branchTable, {
      fields: [academicSessionTable.branchId],
      references: [branchTable.id],
    }),
    classes: many(classTable),
    subjects: many(subjectTable),
    enrollments: many(enrollmentTable),
    sectionSubjects: many(sectionSubjectTable),
    exams: many(examTable),
  }),
);

export const classRelations = relations(classTable, ({ one, many }) => ({
  academicSession: one(academicSessionTable, {
    fields: [classTable.academicSessionId],
    references: [academicSessionTable.id],
  }),
  sections: many(classSectionTable),
}));

export const subjectRelations = relations(subjectTable, ({ one, many }) => ({
  academicSession: one(academicSessionTable, {
    fields: [subjectTable.academicSessionId],
    references: [academicSessionTable.id],
  }),
  sectionSubjects: many(sectionSubjectTable),
  diaryEntries: many(diaryTable),
}));

// ==================== STAFF RELATIONS ====================

export const supportStaffProfileRelations = relations(
  supportStaffProfileTable,
  ({ many }) => ({
    staff: many(staffTable),
  }),
);

export const staffRelations = relations(staffTable, ({ one, many }) => ({
  user: one(usersTable, {
    fields: [staffTable.userId],
    references: [usersTable.id],
  }),
  branch: one(branchTable, {
    fields: [staffTable.branchId],
    references: [branchTable.id],
  }),
  supportStaffProfile: one(supportStaffProfileTable, {
    fields: [staffTable.supportStaffProfileId],
    references: [supportStaffProfileTable.id],
  }),
  classSections: many(classSectionTable),
  sectionSubjects: many(sectionSubjectTable),
  createdExams: many(examTable),
  createdExamSchedules: many(examScheduleTable),
  createdExamResults: many(examResultTable),
}));

// ==================== STUDENT RELATIONS ====================

export const guardianRelations = relations(guardianTable, ({ one, many }) => ({
  user: one(usersTable, {
    fields: [guardianTable.userId],
    references: [usersTable.id],
  }),
  students: many(studentTable),
}));

export const studentRelations = relations(studentTable, ({ one, many }) => ({
  guardian: one(guardianTable, {
    fields: [studentTable.guardianId],
    references: [guardianTable.userId],
  }),
  classSection: one(classSectionTable, {
    fields: [studentTable.classSectionId],
    references: [classSectionTable.id],
  }),
  enrollments: many(enrollmentTable),
}));

export const enrollmentRelations = relations(
  enrollmentTable,
  ({ one, many }) => ({
    student: one(studentTable, {
      fields: [enrollmentTable.studentId],
      references: [studentTable.id],
    }),
    classSection: one(classSectionTable, {
      fields: [enrollmentTable.classSectionId],
      references: [classSectionTable.id],
    }),
    academicSession: one(academicSessionTable, {
      fields: [enrollmentTable.academicSessionId],
      references: [academicSessionTable.id],
    }),
    examResults: many(examResultTable),
  }),
);

// ==================== EDUCATION RELATIONS ====================

export const classSectionRelations = relations(
  classSectionTable,
  ({ one, many }) => ({
    class: one(classTable, {
      fields: [classSectionTable.classId],
      references: [classTable.id],
    }),
    classTeacher: one(staffTable, {
      fields: [classSectionTable.classTeacherId],
      references: [staffTable.id],
    }),
    students: many(studentTable),
    enrollments: many(enrollmentTable),
    sectionSubjects: many(sectionSubjectTable),
    diaryEntries: many(diaryTable),
    exams: many(examTable),
  }),
);

export const sectionSubjectRelations = relations(
  sectionSubjectTable,
  ({ one, many }) => ({
    classSection: one(classSectionTable, {
      fields: [sectionSubjectTable.classSectionId],
      references: [classSectionTable.id],
    }),
    subject: one(subjectTable, {
      fields: [sectionSubjectTable.subjectId],
      references: [subjectTable.id],
    }),
    academicSession: one(academicSessionTable, {
      fields: [sectionSubjectTable.academicSessionId],
      references: [academicSessionTable.id],
    }),
    subjectTeacher: one(staffTable, {
      fields: [sectionSubjectTable.subjectTeacherId],
      references: [staffTable.id],
    }),
    examSchedules: many(examScheduleTable),
  }),
);

export const diaryRelations = relations(diaryTable, ({ one }) => ({
  classSection: one(classSectionTable, {
    fields: [diaryTable.classSectionId],
    references: [classSectionTable.id],
  }),
  subject: one(subjectTable, {
    fields: [diaryTable.subjectId],
    references: [subjectTable.id],
  }),
}));

// ==================== ATTENDANCE RELATIONS ====================

export const attendanceStudentRelation = relations(
  studentAttendanceTable,
  ({ one }) => ({
    enrollment: one(enrollmentTable, {
      fields: [studentAttendanceTable.enrollmentId],
      references: [enrollmentTable.id],
    }),
    markedBy: one(staffTable, {
      fields: [studentAttendanceTable.marketedById],
      references: [staffTable.id],
    }),
  }),
);

export const enrollmentAttendanceRelations = relations(
  enrollmentTable,
  ({ many }) => ({
    attendances: many(studentAttendanceTable),
  }),
);

export const staffAttendanceRelations = relations(staffTable, ({ many }) => ({
  attendancesMarked: many(studentAttendanceTable),
}));

// ==================== EXAMS RELATIONS ====================
export const examRelations = relations(examTable, ({ one, many }) => ({
  academicSession: one(academicSessionTable, {
    fields: [examTable.academicSessionId],
    references: [academicSessionTable.id],
  }),
  createdBy: one(staffTable, {
    fields: [examTable.createdBy],
    references: [staffTable.id],
  }),
  classSections: many(classSectionExamTable),
}));

export const classSectionExamRelations = relations(
  classSectionExamTable,
  ({ one, many }) => ({
    classSection: one(classSectionTable, {
      fields: [classSectionExamTable.classSectionId],
      references: [classSectionTable.id],
    }),
    exam: one(examTable, {
      fields: [classSectionExamTable.examId],
      references: [examTable.id],
    }),
    examSchedules: many(examScheduleTable),
  }),
);

export const examScheduleRelations = relations(
  examScheduleTable,
  ({ one, many }) => ({
    classSectionExam: one(classSectionExamTable, {
      fields: [examScheduleTable.classSectionExamId],
      references: [classSectionExamTable.id],
    }),
    sectionSubject: one(sectionSubjectTable, {
      fields: [examScheduleTable.sectionSubjectId],
      references: [sectionSubjectTable.id],
    }),
    createdBy: one(staffTable, {
      fields: [examScheduleTable.createdBy],
      references: [staffTable.id],
    }),
    examResults: many(examResultTable),
  }),
);

export const examResultRelations = relations(examResultTable, ({ one }) => ({
  examSchedule: one(examScheduleTable, {
    fields: [examResultTable.examScheduleId],
    references: [examScheduleTable.id],
  }),
  enrollment: one(enrollmentTable, {
    fields: [examResultTable.enrollmentId],
    references: [enrollmentTable.id],
  }),
  createdBy: one(staffTable, {
    fields: [examResultTable.createdBy],
    references: [staffTable.id],
  }),
}));

// ==================== STUDENT CARD TEMPLATE RELATIONS ====================

export const studentCardTemplateRelations = relations(
  studentCardTemplateTable,
  ({ one }) => ({
    branch: one(branchTable, {
      fields: [studentCardTemplateTable.branchId],
      references: [branchTable.id],
    }),
  }),
);
