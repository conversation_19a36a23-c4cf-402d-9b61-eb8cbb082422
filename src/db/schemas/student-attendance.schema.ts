import * as t from "drizzle-orm/pg-core";
import { enrollmentTable, staffTable } from "./index.js";
import { timestamps } from "../helpers/columns.helpers.js";

export const attendanceStatusEnum = t.pgEnum("attendance_status", [
  "PRESENT",
  "ABSENT",
  "LATE",
  "LEAVE",
]);

export const studentAttendanceTable = t.pgTable(
  "student_attendance",
  {
    id: t.uuid("id").primaryKey().defaultRandom(),
    enrollmentId: t
      .uuid("enrollment_id")
      .notNull()
      .references(() => enrollmentTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    date: t.date("date").notNull(),
    status: attendanceStatusEnum("status").notNull(),
    remarks: t.text("remarks"),
    marketedById: t
      .uuid("created_by")
      .notNull()
      .references(() => staffTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    checkedInTime: t.timestamp("checked_in_time", { withTimezone: true }),
    checkedOutTime: t.timestamp("checked_out_time", { withTimezone: true }),
    ...timestamps,
  },
  table => [
    // Unique constraint for student per date
    t
      .unique("unique_student_per_date_per_class_section")
      .on(table.enrollmentId, table.date),
  ],
);
