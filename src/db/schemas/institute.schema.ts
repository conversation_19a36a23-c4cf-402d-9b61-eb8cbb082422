import {
  pgTable,
  uuid,
  text,
  boolean,
  timestamp,
  unique,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { usersTable } from "./auth.schema.js";

// ==================== TABLES ====================

/**
 * Institute owners table
 * Links users to institute owner role with setup completion tracking
 */
export const instituteOwnerTable = pgTable("institute_owner", {
  userId: uuid("user_id")
    .primaryKey()
    .references(() => usersTable.id, {
      onDelete: "restrict",
      onUpdate: "cascade",
    }),
  createdAt: timestamp("created_at", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
});

/**
 * Institutes table
 * Core institute information and settings
 */
export const instituteTable = pgTable("institute", {
  id: uuid("id")
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  name: text("name").notNull().unique(),
  email: text("email").notNull().unique(),
  logo: text("logo"),
  isActive: boolean("is_active").notNull().default(true),
  isBasicSetupComplete: boolean("is_basic_setup_complete")
    .notNull()
    .default(false),
  ownerId: uuid("owner_id")
    .notNull()
    .unique()
    .references(() => instituteOwnerTable.userId, {
      onDelete: "restrict",
      onUpdate: "cascade",
    }),
  createdAt: timestamp("created_at", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
});

/**
 * Branches table
 * Multiple locations/branches for each institute
 */
export const branchTable = pgTable(
  "branch",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    email: text("email").notNull(),
    name: text("name").notNull().unique(),
    address: text("address").notNull(),
    phone: text("phone").notNull(),
    isMain: boolean("is_main").notNull().default(false),
    isActive: boolean("is_active").notNull().default(true),
    instituteId: uuid("institute_id")
      .notNull()
      .references(() => instituteTable.id),
    createdAt: timestamp("created_at", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
  },
  table => [
    // Unique constraint to ensure only one main branch per institute
    unique("branch_is_main_unique_index").on(table.isMain, table.instituteId),
  ],
);

// ==================== TYPES ====================

/**
 * Type definitions for the institute schema tables
 */
export type InstituteOwner = typeof instituteOwnerTable.$inferSelect;
export type NewInstituteOwner = typeof instituteOwnerTable.$inferInsert;

export type Institute = typeof instituteTable.$inferSelect;
export type NewInstitute = typeof instituteTable.$inferInsert;

export type Branch = typeof branchTable.$inferSelect;
export type NewBranch = typeof branchTable.$inferInsert;
