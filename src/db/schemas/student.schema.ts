import {
  pgTable,
  uuid,
  text,
  date,
  timestamp,
  unique,
  integer,
  pgEnum,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { usersTable, genderEnum } from "./auth.schema.js";
import { academicSessionTable } from "./academic.schema.js";

// ==================== ENUMS ====================

/**
 * Guardian relation enumeration
 */
export const guardianRelationEnum = pgEnum("guardian_relation", [
  "FATHER",
  "MOTHER",
  "GUARDIAN",
]);

/**
 * Religion enumeration
 */
export const religionEnum = pgEnum("religion", [
  "ISLAM",
  "CHRISTIANITY",
  "HINDUISM",
  "BUDDHISM",
  "SIKHISM",
  "JUDAISM",
  "OTHER",
]);

/**
 * Enrollment type enumeration
 */
export const enrollmentTypeEnum = pgEnum("enrollment_type", [
  "ADMISSION",
  "TRANSFER_IN",
  "REPEATING",
  "PROMOTION",
]);

/**
 * Enrollment status enumeration
 */
export const enrollmentStatusEnum = pgEnum("enrollment_status", [
  "ACTIVE",
  "EXPELLED",
  "GRADUATED",
  "DECEASED",
  "COMPLETED",
  "WITHDRAWN",
]);

// ==================== TABLES ====================

/**
 * Guardian table
 * Links users to guardian role with relationship information
 */
export const guardianTable = pgTable("guardian", {
  userId: uuid("user_id")
    .primaryKey()
    .references(() => usersTable.id, {
      onDelete: "restrict",
      onUpdate: "cascade",
    }),
  relation: guardianRelationEnum("relation").notNull(),
  createdAt: timestamp("created_at", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
});

/**
 * Student table
 * Student profiles and information
 * Note: classSectionId will be referenced from education.schema.ts
 */
export const studentTable = pgTable("student", {
  id: uuid("id")
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  name: text("name").notNull(),
  registrationNumber: text("registration_number"),
  rollNumber: integer("roll_number").notNull(),
  email: text("email").unique(),
  fatherName: text("father_name").notNull(),
  address: text("address").notNull(),
  gender: genderEnum("gender").notNull(),
  photo: text("photo"),
  religion: religionEnum("religion").notNull(),
  monthlyFee: integer("monthly_fee").notNull(),
  admissionDate: date("admission_date").notNull(),
  dateOfBirth: date("date_of_birth").notNull(),
  previousSchool: text("previous_school"),
  classSectionId: uuid("class_section_id").notNull(),
  // Note: Foreign key reference to classSectionTable will be added via relations
  guardianId: uuid("guardian_id")
    .notNull()
    .references(() => guardianTable.userId, {
      onDelete: "restrict",
      onUpdate: "cascade",
    }),
  createdAt: timestamp("created_at", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
});

/**
 * Enrollment table
 * Tracks student enrollments across academic sessions
 * Note: classSectionId will be referenced from education.schema.ts
 */
export const enrollmentTable = pgTable(
  "enrollment",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    studentId: uuid("student_id")
      .notNull()
      .references(() => studentTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    classSectionId: uuid("class_section_id").notNull(),
    // Note: Foreign key reference will be added in education.schema.ts
    academicSessionId: uuid("academic_session_id")
      .notNull()
      .references(() => academicSessionTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    type: enrollmentTypeEnum("type").notNull(),
    status: enrollmentStatusEnum("status").notNull(),
    date: date("date").notNull(),
    createdAt: timestamp("created_at", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
  },
  table => [
    // Unique constraint for student per academic session
    unique("unique_student_session_per_enrollment").on(
      table.studentId,
      table.academicSessionId,
    ),
    // Unique index for non-repeating students per section
    unique("idx_unique_student_per_section_if_not_repeating").on(
      table.studentId,
      table.classSectionId,
      table.type,
    ),
    // Unique index for active status per session
    unique("idx_unique_academic_session_status").on(
      table.academicSessionId,
      table.status,
      table.studentId,
    ),
  ],
);

// ==================== TYPES ====================

/**
 * Type definitions for the student schema tables
 */
export type Guardian = typeof guardianTable.$inferSelect;
export type NewGuardian = typeof guardianTable.$inferInsert;

export type Student = typeof studentTable.$inferSelect;
export type NewStudent = typeof studentTable.$inferInsert;

export type Enrollment = typeof enrollmentTable.$inferSelect;
export type NewEnrollment = typeof enrollmentTable.$inferInsert;
