import { sql } from "drizzle-orm";
import * as t from "drizzle-orm/pg-core";
import { primaryKey, timestamps } from "../helpers/columns.helpers.js";
import { academicSessionTable } from "./academic.schema.js";
import { staffTable } from "./staff.schema.js";
import { classSectionTable, sectionSubjectTable } from "./education.schema.js";
import { enrollmentTable } from "./student.schema.js";

export const examTable = t.pgTable(
  "exam",
  {
    id: primaryKey,
    name: t.text("name").notNull(),
    startDate: t.date("start_date"),
    endDate: t.date("end_date"),
    academicSessionId: t
      .uuid("academic_session_id")
      .notNull()
      .references(() => academicSessionTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    createdBy: t
      .uuid("created_by")
      .notNull()
      .references(() => staffTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    ...timestamps,
  },
  table => [
    // Check constraint for start date before end date
    t.check(
      "start_date_before_end_date",
      sql`${table.startDate} IS NULL OR ${table.endDate} IS NULL OR ${table.startDate} < ${table.endDate}`,
    ),
    // unique constraint for exam name within academic session
    t
      .unique("unique_exam_name_per_academic_session")
      .on(table.name, table.academicSessionId),
  ],
);

export const classSectionExamTable = t.pgTable(
  "class_section_exam",
  {
    id: primaryKey,
    classSectionId: t
      .uuid("class_section_id")
      .notNull()
      .references(() => classSectionTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    examId: t
      .uuid("exam_id")
      .notNull()
      .references(() => examTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    ...timestamps,
  },
  table => [
    // unique constraint for class section per exam
    t
      .unique("unique_class_section_per_exam")
      .on(table.classSectionId, table.examId),
  ],
);

export const examScheduleTable = t.pgTable(
  "exam_schedule",
  {
    id: primaryKey,
    classSectionExamId: t
      .uuid("class_section_exam_id")
      .notNull()
      .references(() => classSectionExamTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    date: t.date("date").notNull(),
    startTime: t.timestamp("start_time", { withTimezone: true }).notNull(),
    endTime: t.timestamp("end_time", { withTimezone: true }).notNull(),
    sectionSubjectId: t
      .uuid("section_subject_id")
      .notNull()
      .references(() => sectionSubjectTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    totalMarks: t.integer("total_marks").notNull(),
    passingMarks: t.integer("passing_marks").notNull(),
    createdBy: t
      .uuid("created_by")
      .notNull()
      .references(() => staffTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    ...timestamps,
  },
  table => [
    // unique constraint for class section per exam
    t
      .unique("unique_exam_schedule_date_subject")
      .on(table.classSectionExamId, table.date, table.sectionSubjectId),
    // Only one exam per date and time
    t
      .unique("unique_exam_date_time")
      .on(table.date, table.startTime, table.endTime),
    // Check constraint for start time before end time
    t.check(
      "start_time_before_end_time",
      sql`${table.startTime} < ${table.endTime}`,
    ),

    // Check constraint for total marks greater than passing marks
    t.check(
      "total_marks_greater_than_or_equal_to_passing_marks",
      sql`${table.totalMarks} >= ${table.passingMarks}`,
    ),
  ],
);

export const examResultTable = t.pgTable(
  "exam_result",
  {
    id: primaryKey,
    examScheduleId: t
      .uuid("exam_schedule_id")
      .notNull()
      .references(() => examScheduleTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    enrollmentId: t
      .uuid("enrollment_id")
      .notNull()
      .references(() => enrollmentTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    marksObtained: t.integer("marks_obtained"),
    remarks: t.text("remarks"),
    isAbsent: t.boolean("is_absent").notNull().default(false),
    createdBy: t
      .uuid("created_by")
      .notNull()
      .references(() => staffTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    ...timestamps,
  },
  table => [
    // unique constraint for student per exam schedule
    t
      .unique("unique_student_per_exam_schedule")
      .on(table.enrollmentId, table.examScheduleId),

    t.check(
      "marks_obtained_cannot_be_negative",
      sql`${table.marksObtained} >= 0`,
    ),
  ],
);
