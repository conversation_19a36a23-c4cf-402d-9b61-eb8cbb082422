import {
  pgTable,
  uuid,
  text,
  date,
  boolean,
  timestamp,
  integer,
  unique,
  index,
  check,
  pgEnum,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { branchTable } from "./institute.schema.js";

// ==================== ENUMS ====================

/**
 * Subject type enumeration for academic subjects
 */
export const subjectTypeEnum = pgEnum("subject_type", ["THEORY", "PRACTICAL"]);

// ==================== TABLES ====================

/**
 * Academic sessions table
 * Defines academic years/terms for each branch
 */
export const academicSessionTable = pgTable(
  "academic_session",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    name: text("name").notNull(),
    startDate: date("start_date").notNull(),
    endDate: date("end_date").notNull(),
    branchId: uuid("branch_id")
      .notNull()
      .references(() => branchTable.id),
    isActive: boolean("is_active").notNull().default(false),
    createdAt: timestamp("created_at", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
  },
  table => [
    // Check constraint to ensure start date is before end date
    check(
      "start_date_before_end_date",
      sql`${table.startDate} < ${table.endDate}`,
    ),
    // Unique constraint for academic session dates
    unique("academic_session_date_unique").on(table.startDate, table.endDate),
    // Unique index to ensure only one active session per branch
    unique("academic_session_is_active_index").on(
      table.branchId,
      table.isActive,
    ),
  ],
);

/**
 * Classes table
 * Academic classes/grades within each session
 */
export const classTable = pgTable(
  "class",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    name: text("name").notNull(),
    maximumStudents: integer("maximum_students").notNull(),
    isActive: boolean("is_active").notNull().default(true),
    academicSessionId: uuid("academic_session_id")
      .notNull()
      .references(() => academicSessionTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    feePerMonth: integer("fee_per_month").notNull(),
    createdAt: timestamp("created_at", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
  },
  table => [
    // Unique constraint for class name within academic session
    unique("class_name_academic_session_id_unique").on(
      table.name,
      table.academicSessionId,
    ),
  ],
);

/**
 * Subjects table
 * Academic subjects for each session
 */
export const subjectTable = pgTable(
  "subject",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    name: text("name").notNull(),
    marks: integer("marks").notNull(),
    isActive: boolean("is_active").notNull().default(true),
    academicSessionId: uuid("academic_session_id")
      .notNull()
      .references(() => academicSessionTable.id),
    type: subjectTypeEnum("type").notNull(),
    createdAt: timestamp("created_at", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
  },
  table => [
    // Unique index for subject name, session, and type combination
    unique("idx_subject_name_session_id_type").on(
      table.name,
      table.academicSessionId,
      table.type,
    ),
    // Index for academic session queries
    index("idx_subject_session_id").on(table.academicSessionId),
  ],
);

// ==================== TYPES ====================

/**
 * Type definitions for the academic schema tables
 */
export type AcademicSession = typeof academicSessionTable.$inferSelect;
export type NewAcademicSession = typeof academicSessionTable.$inferInsert;

export type Class = typeof classTable.$inferSelect;
export type NewClass = typeof classTable.$inferInsert;

export type Subject = typeof subjectTable.$inferSelect;
export type NewSubject = typeof subjectTable.$inferInsert;
