import {
  pgTable,
  uuid,
  text,
  numeric,
  integer,
  timestamp,
  varchar,
  check,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { usersTable } from "./auth.schema.js";

// ==================== TABLES ====================

/**
 * Platform administrators table
 * Links users to platform admin role with additional metadata
 */
export const platformAdminTable = pgTable("platform_admin", {
  userId: uuid("user_id")
    .primaryKey()
    .references(() => usersTable.id, {
      onDelete: "restrict",
      onUpdate: "cascade",
    }),
  createdAt: timestamp("created_at", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
});

/**
 * Subscription plans table
 * Defines different service packages available for institutes
 */
export const subscriptionPlanTable = pgTable("subscription_plan", {
  id: uuid("id")
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  title: text("title").notNull().unique(),
  description: text("description"),
  price: numeric("price", { precision: 10, scale: 2 }).notNull().unique(),
  setupCharges: numeric("setup_charges", { precision: 10, scale: 2 }).notNull(),
  branches: integer("branches").notNull(),
  students: integer("students").notNull(),
  features: text("features").array().notNull(),
  createdAt: timestamp("created_at", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
});

/**
 * Subscriptions table
 * Tracks active subscriptions for institute owners
 */
export const subscriptionTable = pgTable(
  "subscription",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    planId: uuid("plan_id")
      .notNull()
      .references(() => subscriptionPlanTable.id),
    ownerId: uuid("owner_id").notNull().unique(),
    status: varchar("status", { length: 20 }).notNull(),
    startDate: timestamp("start_date", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
    paymentCycle: varchar("payment_cycle", { length: 20 }).notNull(),
    gracePeriodDays: integer("grace_period_days").notNull().default(5),
    endDate: timestamp("end_date", { withTimezone: true }).notNull(),
    nextPaymentDate: timestamp("next_payment_date", {
      withTimezone: true,
    }).notNull(),
    lastPaymentDate: timestamp("last_payment_date", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
    cancellationDate: timestamp("cancellation_date", { withTimezone: true }),
    createdAt: timestamp("created_at", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
  },
  table => [
    // Check constraints for status and payment cycle
    check(
      "status_check",
      sql`${table.status} IN ('active', 'cancelled', 'unpaid', 'expired')`,
    ),
    check(
      "payment_cycle_check",
      sql`${table.paymentCycle} IN ('monthly', 'yearly')`,
    ),
  ],
);

// ==================== TYPES ====================

/**
 * Type definitions for the platform schema tables
 */
export type PlatformAdmin = typeof platformAdminTable.$inferSelect;
export type NewPlatformAdmin = typeof platformAdminTable.$inferInsert;

export type SubscriptionPlan = typeof subscriptionPlanTable.$inferSelect;
export type NewSubscriptionPlan = typeof subscriptionPlanTable.$inferInsert;

export type Subscription = typeof subscriptionTable.$inferSelect;
export type NewSubscription = typeof subscriptionTable.$inferInsert;
