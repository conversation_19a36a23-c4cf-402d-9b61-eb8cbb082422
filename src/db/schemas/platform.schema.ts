import {
  pgTable,
  uuid,
  text,
  numeric,
  integer,
  timestamp,
  varchar,
  check,
  pgEnum,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { usersTable } from "./auth.schema.js";

// ==================== TABLES ====================

/**
 * Platform administrators table
 * Links users to platform admin role with additional metadata
 */
export const platformAdminTable = pgTable("platform_admin", {
  userId: uuid("user_id")
    .primaryKey()
    .references(() => usersTable.id, {
      onDelete: "restrict",
      onUpdate: "cascade",
    }),
  createdAt: timestamp("created_at", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
});

/**
 * Subscription plans table
 * Defines different service packages available for institutes
 */
export const subscriptionPlanTable = pgTable("subscription_plan", {
  id: uuid("id")
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  title: text("title").notNull().unique(),
  description: text("description"),
  price: integer("price").notNull().unique(),
  setupCharges: integer("setup_charges").notNull(),
  branches: integer("branches").notNull(),
  students: integer("students").notNull(),
  features: text("features").array().notNull(),
  createdAt: timestamp("created_at", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
});

export const subscriptionStatusEnum = pgEnum("subscription_status", [
  "ACTIVE",
  "CANCELLED",
  "UNPAID",
  "EXPIRED",
]);

export const subscriptionPaymentCycleEnum = pgEnum(
  "subscription_payment_cycle",
  ["MONTHLY", "YEARLY"],
);

/**
 * Subscriptions table
 * Tracks active subscriptions for institute owners
 */
export const subscriptionTable = pgTable("subscription", {
  id: uuid("id")
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  planId: uuid("plan_id")
    .notNull()
    .references(() => subscriptionPlanTable.id),
  ownerId: uuid("owner_id").notNull().unique(),
  status: subscriptionStatusEnum("status").notNull(),
  startDate: timestamp("start_date", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
  paymentCycle: subscriptionPaymentCycleEnum("payment_cycle").notNull(),
  endDate: timestamp("end_date", { withTimezone: true }).notNull(),
  lastPaymentDate: timestamp("last_payment_date", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
  cancellationDate: timestamp("cancellation_date", { withTimezone: true }),
  createdAt: timestamp("created_at", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
});
