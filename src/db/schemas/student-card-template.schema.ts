import * as t from "drizzle-orm/pg-core";
import { timestamps } from "../helpers/columns.helpers.js";
import { branchTable } from "./institute.schema.js";

export const studentCardTemplateTable = t.pgTable("student_card_template", {
  id: t.uuid("id").primaryKey().defaultRandom(),
  title: t.text("title"),
  watermark: t.text("watermark"),
  headerImage: t.text("header_image"),
  backgroundImage: t.text("background_image").notNull(),
  branchId: t
    .uuid("branch_id")
    .notNull()
    .references(() => branchTable.id, {
      onDelete: "restrict",
      onUpdate: "cascade",
    })
    .unique(),
  ...timestamps,
});
